{"version": 3, "file": "KHR_materials_clearcoat-BYw4Pw3F.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_clearcoat.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_clearcoat\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_clearcoat/README.md)\n * [Playground Sample](https://www.babylonjs-playground.com/frame.html#7F7PN6#8)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_clearcoat {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 190;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadClearCoatPropertiesAsync(extensionContext, extension, babylonMaterial));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadClearCoatPropertiesAsync(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        babylonMaterial.clearCoat.isEnabled = true;\n        babylonMaterial.clearCoat.useRoughnessFromMainTexture = false;\n        babylonMaterial.clearCoat.remapF0OnInterfaceChange = false;\n        if (properties.clearcoatFactor != undefined) {\n            babylonMaterial.clearCoat.intensity = properties.clearcoatFactor;\n        }\n        else {\n            babylonMaterial.clearCoat.intensity = 0;\n        }\n        if (properties.clearcoatTexture) {\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/clearcoatTexture`, properties.clearcoatTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (ClearCoat)`;\n                babylonMaterial.clearCoat.texture = texture;\n            }));\n        }\n        if (properties.clearcoatRoughnessFactor != undefined) {\n            babylonMaterial.clearCoat.roughness = properties.clearcoatRoughnessFactor;\n        }\n        else {\n            babylonMaterial.clearCoat.roughness = 0;\n        }\n        if (properties.clearcoatRoughnessTexture) {\n            properties.clearcoatRoughnessTexture.nonColorData = true;\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/clearcoatRoughnessTexture`, properties.clearcoatRoughnessTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (ClearCoat Roughness)`;\n                babylonMaterial.clearCoat.textureRoughness = texture;\n            }));\n        }\n        if (properties.clearcoatNormalTexture) {\n            properties.clearcoatNormalTexture.nonColorData = true;\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/clearcoatNormalTexture`, properties.clearcoatNormalTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (ClearCoat Normal)`;\n                babylonMaterial.clearCoat.bumpTexture = texture;\n            }));\n            babylonMaterial.invertNormalMapX = !babylonMaterial.getScene().useRightHandedSystem;\n            babylonMaterial.invertNormalMapY = babylonMaterial.getScene().useRightHandedSystem;\n            if (properties.clearcoatNormalTexture.scale != undefined) {\n                babylonMaterial.clearCoat.bumpTexture.level = properties.clearcoatNormalTexture.scale;\n            }\n        }\n        return Promise.all(promises).then(() => { });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_clearcoat(loader));\n//# sourceMappingURL=KHR_materials_clearcoat.js.map"], "names": ["NAME", "KHR_materials_clearcoat", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "properties", "PBRMaterial", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "gTAGA,MAAMA,EAAO,0BAMN,MAAMC,CAAwB,CAIjC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,8BAA8BF,EAAkBC,EAAWH,CAAe,CAAC,EACvF,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,8BAA8BN,EAASO,EAAYL,EAAiB,CAChE,GAAI,EAAEA,aAA2BM,GAC7B,MAAM,IAAI,MAAM,GAAGR,CAAO,+BAA+B,EAE7D,MAAMM,EAAW,IAAI,MACrB,OAAAJ,EAAgB,UAAU,UAAY,GACtCA,EAAgB,UAAU,4BAA8B,GACxDA,EAAgB,UAAU,yBAA2B,GACjDK,EAAW,iBAAmB,KAC9BL,EAAgB,UAAU,UAAYK,EAAW,gBAGjDL,EAAgB,UAAU,UAAY,EAEtCK,EAAW,kBACXD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,oBAAqBO,EAAW,iBAAmBE,GAAY,CACrHA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,eACtCA,EAAgB,UAAU,QAAUO,CACvC,CAAA,CAAC,EAEFF,EAAW,0BAA4B,KACvCL,EAAgB,UAAU,UAAYK,EAAW,yBAGjDL,EAAgB,UAAU,UAAY,EAEtCK,EAAW,4BACXA,EAAW,0BAA0B,aAAe,GACpDD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,6BAA8BO,EAAW,0BAA4BE,GAAY,CACvIA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,yBACtCA,EAAgB,UAAU,iBAAmBO,CAChD,CAAA,CAAC,GAEFF,EAAW,yBACXA,EAAW,uBAAuB,aAAe,GACjDD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,0BAA2BO,EAAW,uBAAyBE,GAAY,CACjIA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,sBACtCA,EAAgB,UAAU,YAAcO,CAC3C,CAAA,CAAC,EACFP,EAAgB,iBAAmB,CAACA,EAAgB,SAAQ,EAAG,qBAC/DA,EAAgB,iBAAmBA,EAAgB,SAAQ,EAAG,qBAC1DK,EAAW,uBAAuB,OAAS,OAC3CL,EAAgB,UAAU,YAAY,MAAQK,EAAW,uBAAuB,QAGjF,QAAQ,IAAID,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CAC9C,CACL,CACAI,EAAwBb,CAAI,EAC5Bc,EAAsBd,EAAM,GAAOE,GAAW,IAAID,EAAwBC,CAAM,CAAC", "x_google_ignoreList": [0]}