import{ar as p,an as m,ao as l}from"./index-SLPGw9aX.js";import{GLTFLoader as h}from"./glTFLoader-CeZAwvqd.js";import"./index-Co_Q4qaw.js";import"./svelte/svelte.js";import"./bone-Ceu782jt.js";import"./rawTexture-BO6WiE_K.js";import"./assetContainer-9eHOOeSf.js";import"./objectModelMapping-Duj8W7QQ.js";const s="KHR_materials_ior";class i{constructor(r){this.name=s,this.order=180,this._loader=r,this.enabled=this._loader.isExtensionUsed(s)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,o,e){return h.LoadExtensionAsync(r,o,this.name,(a,d)=>{const t=new Array;return t.push(this._loader.loadMaterialPropertiesAsync(r,o,e)),t.push(this._loadIorPropertiesAsync(a,d,e)),Promise.all(t).then(()=>{})})}_loadIorPropertiesAsync(r,o,e){if(!(e instanceof p))throw new Error(`${r}: Material type not supported`);return o.ior!==void 0?e.indexOfRefraction=o.ior:e.indexOfRefraction=i._DEFAULT_IOR,Promise.resolve()}}i._DEFAULT_IOR=1.5;m(s);l(s,!0,n=>new i(n));export{i as KHR_materials_ior};
//# sourceMappingURL=KHR_materials_ior-C4Jo3F6p.js.map
