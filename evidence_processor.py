# evidence_processor.py
"""
Evidence processing module for hierarchical evidence synthesis.
Implements incremental processing of research studies for systematic reviews.
"""

import re
import json
import logging
from collections import defaultdict
from dataclasses import dataclass, asdict, field
from enum import Enum
from typing import Dict, List, Any, Optional, Set

import numpy as np

# 假设 llm_manager 可以从项目中导入
# from llm_manager import LLMManager

logger = logging.getLogger(__name__)

class EvidenceDirection(str, Enum):
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    MIXED = "mixed"

@dataclass
class EvidencePoint:
    """Structured representation of a single piece of evidence."""
    study_id: str
    pico: Dict[str, Any]
    finding: str
    direction: EvidenceDirection
    magnitude: float
    quality_score: float
    theme: Optional[str] = None
    visualization_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict:
        data = asdict(self)
        data['direction'] = self.direction.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'EvidencePoint':
        return cls(
            study_id=data['study_id'],
            pico=data['pico'],
            finding=data['finding'],
            direction=EvidenceDirection(data['direction']),
            magnitude=data['magnitude'],
            quality_score=data['quality_score'],
            theme=data.get('theme')
        )

@dataclass
class ThemeSummary:
    """Summary of evidence for a specific theme."""
    name: str
    evidence_points: List[EvidencePoint] = field(default_factory=list)
    main_findings: List[str] = field(default_factory=list)
    consistency_score: float = 0.0
    evidence_strength: float = 0.0
    
    def add_evidence(self, evidence: EvidencePoint):
        self.evidence_points.append(evidence)
        if evidence.finding and evidence.finding != "无" and evidence.finding not in self.main_findings:
            self.main_findings.append(evidence.finding)
        self._update_metrics()
    
    def _update_metrics(self):
        if not self.evidence_points:
            return
            
        directions = [1 if ep.direction == EvidenceDirection.POSITIVE else 
                     -1 if ep.direction == EvidenceDirection.NEGATIVE else 0 
                     for ep in self.evidence_points]
        # 当所有方向都相同时，方差为0，一致性为1。当方向一半正一半负时，方差为1，一致性为0。
        self.consistency_score = 1 - np.var(directions) if len(directions) > 1 else 1.0
        
        total_weight = sum(ep.quality_score * ep.magnitude for ep in self.evidence_points)
        if self.evidence_points:
            self.evidence_strength = total_weight / len(self.evidence_points)
    
    def to_dict(self) -> Dict:
        return {
            'name': self.name,
            'evidence_count': len(self.evidence_points),
            'main_findings': self.main_findings,
            'consistency_score': self.consistency_score,
            'evidence_strength': self.evidence_strength
        }

class EvidenceProcessor:
    """Processes and synthesizes evidence from multiple studies incrementally."""
    
    def __init__(self, llm_manager):
        self.llm = llm_manager
        self.themes: Dict[str, ThemeSummary] = {}
        self.study_ids: Set[str] = set()
    
    def process_study_batch(self, studies: List[Dict[str, Any]], provider: str, model: str) -> Dict:
        updates = {'new_themes': [], 'updated_themes': [], 'evidence_count': 0}
        
        for study in studies:
            if not study or study.get('id') in self.study_ids:
                continue
            
            self.study_ids.add(study['id'])
            evidence_points = self._extract_evidence_points(study, provider, model)
            
            for evidence in evidence_points:
                theme_name = evidence.theme or "Uncategorized"
                
                if theme_name not in self.themes:
                    self.themes[theme_name] = ThemeSummary(name=theme_name)
                    updates['new_themes'].append(theme_name)
                
                self.themes[theme_name].add_evidence(evidence)
                updates['evidence_count'] += 1
                
                if theme_name not in updates['new_themes'] and theme_name not in updates['updated_themes']:
                    updates['updated_themes'].append(theme_name)
        
        return updates
    
    def _is_clinical_study(self, study: Dict[str, Any], provider: str, model: str) -> bool:
        """
        Determine if a study is clinical based on its content and LLM analysis.
        
        Args:
            study: Dictionary containing study information
            provider: LLM provider name
            model: Model name to use
            
        Returns:
            bool: True if the study is clinical, False otherwise
        """
        try:
            # First check: Basic validation
            if not study:
                return False
                
            abstract = str(study.get('abstract', '')).lower()
            title = str(study.get('title', '')).lower()
            
            # If we have neither title nor abstract, we can't determine if it's clinical
            if not abstract and not title:
                return False
            
            # Second check: Look for clinical terms in title/abstract
            clinical_terms = [
                'clinical trial', 'randomized controlled trial', 'rct', 'cohort study',
                'case-control', 'cross-sectional', 'randomized', 'controlled trial',
                'clinical study', 'patients', 'participants', 'subjects', 'clinical research',
                '疗效', '随机对照', '临床试验', '病例对照', '队列研究',
                '横断面研究', '临床研究', '患者', '受试者'
            ]
            
            # Check if any clinical term exists in title or abstract
            search_text = f"{title} {abstract}"
            if any(term in search_text for term in clinical_terms):
                return True
            
            # If no obvious clinical terms found, use LLM for more nuanced analysis
            try:
                prompt = (
                    "请判断以下研究是否为临床研究。请只回答'是'或'否'。\n\n"
                    f"标题: {study.get('title', '无')}\n"
                    f"摘要: {study.get('abstract', '无')}\n\n"
                    "请只回答'是'或'否'："
                )
                
                system_prompt = (
                    "你是一个专业的医学研究助手。你的任务是判断给定的研究是否为临床研究。"
                    "临床研究通常涉及人类受试者、患者或健康志愿者，并可能包括干预措施、观察性研究、"
                    "临床试验等。如果研究涉及患者数据、临床结果或医疗干预，请回答'是'。"
                    "如果研究是基础科学研究、动物研究、综述文章或与临床实践无关，请回答'否'。"
                    "请只回答'是'或'否'，不要包含其他内容。"
                )
                
                # Get LLM response with retry logic
                max_retries = 2
                for attempt in range(max_retries):
                    try:
                        response = self.llm.generate(
                            provider,
                            model,
                            system_prompt=system_prompt,
                            user_prompt=prompt,
                            temperature=0.0,
                            max_tokens=10
                        )
                        
                        # Clean and validate response
                        response = response.strip().lower()
                        if '是' in response or 'yes' in response or 'true' in response:
                            return True
                        if '否' in response or 'no' in response or 'false' in response:
                            return False
                            
                        # If we get here, the response wasn't clear
                        logger.warning(f"Unexpected LLM response for clinical study check: '{response}'")
                        if attempt < max_retries - 1:
                            continue
                            
                        # Default to False if we can't determine after retries
                        return False
                        
                    except Exception as e:
                        logger.warning(f"LLM call failed in _is_clinical_study (attempt {attempt + 1}): {str(e)}")
                        if attempt == max_retries - 1:  # Last attempt
                            logger.error(f"Final LLM call failed for study {study.get('id', 'unknown')}: {str(e)}")
                            return False  # Default to non-clinical on error
                        continue
                
                return False  # Default to non-clinical if all else fails
                
            except Exception as llm_error:
                logger.error(f"Error in LLM-based clinical study check: {str(llm_error)}")
                return False  # Default to non-clinical on error
                
        except Exception as e:
            study_id = study.get('id', 'unknown') if isinstance(study, dict) else 'unknown'
            logger.error(f"Error in _is_clinical_study for study {study_id}: {str(e)}", exc_info=True)
            return False  # Default to non-clinical on error
    
    def _extract_evidence_points(self, study: Dict[str, Any], provider: str, model: str) -> List[EvidencePoint]:
        """
        Extract evidence points from a study using LLM analysis.
        
        Args:
            study: Dictionary containing study information
            provider: LLM provider name
            model: Model name to use
            
        Returns:
            List of EvidencePoint objects extracted from the study
        """
        study_id = study.get('id', 'unknown')
        
        try:
            # First check if this is a clinical study
            if not self._is_clinical_study(study, provider, model):
                logger.debug(f"研究ID {study_id} 不是临床研究，跳过")
                return []
            
            # Prepare the prompt for PICO extraction
            title = study.get('title', '无')
            abstract = study.get('abstract', '无')
            
            prompt = (
                "请从以下临床研究摘要中提取PICO信息。请以JSON格式返回，包含以下字段：\n"
                "- population (人群/患者特征)"
                "- intervention (干预措施)"
                "- comparison (对照/比较组)"
                "- outcome (主要结果)"
                "- finding (研究发现/结论)"
                "- study_design (研究设计)"
                "- sample_size (样本量)"
                "- quality_indicators (质量指标，如随机、盲法等)\n\n"
                f"标题: {title}\n"
                f"摘要: {abstract}\n\n"
                "请确保返回有效的JSON格式，不要包含其他文本。"
            )
            
            system_prompt = (
                "你是一个专业的医学研究助手，擅长从临床研究文献中提取PICO信息。\n"
                "请仔细阅读研究标题和摘要，提取以下信息：\n"
                "1. 研究人群（population）\n"
                "2. 干预措施（intervention）\n"
                "3. 对照/比较组（comparison）\n"
                "4. 主要结果（outcome）\n"
                "5. 研究发现/结论（finding）\n"
                "6. 研究设计（study_design）\n"
                "7. 样本量（sample_size）\n"
                "8. 质量指标（quality_indicators）\n\n"
                "请确保所有字段都包含在返回的JSON中。如果某些信息缺失，请使用'无'。"
            )
            
            # Default values for PICO data
            default_pico = {
                k: "无" for k in [
                    "population", "intervention", "comparison", "outcome", 
                    "finding", "study_design", "sample_size", "quality_indicators"
                ]
            }
            
            # Try to extract PICO data with retries
            max_retries = 2
            pico_data = default_pico
            
            for attempt in range(max_retries):
                try:
                    # Get LLM response
                    response = self.llm.generate(
                        provider,  # Pass provider as positional argument
                        model,  # Pass model as positional argument
                        system_prompt=system_prompt,
                        user_prompt=prompt,
                        temperature=0.1,
                        max_tokens=1000
                    )
                    
                    # Clean and parse the response
                    response = response.strip()
                    
                    # Try to extract JSON from markdown code block
                    json_match = re.search(r'```(?:json)?\n([\s\S]*?)\n```', response)
                    if json_match:
                        json_str = json_match.group(1)
                    else:
                        # If no code block, try to find JSON in the response
                        json_match_alt = re.search(r'\{[\s\S]*\}', response)
                        if json_match_alt:
                            json_str = json_match_alt.group(0)
                        else:
                            raise json.JSONDecodeError("No JSON found in response", response, 0)
                    
                    # Parse and validate the JSON
                    extracted_data = json.loads(json_str)
                    if not isinstance(extracted_data, dict):
                        raise ValueError("Extracted data is not a JSON object")
                    
                    # Merge with defaults, ensuring all required fields exist
                    pico_data = {**default_pico, **{
                        k: str(v).strip() if v is not None else "无" 
                        for k, v in extracted_data.items()
                    }}
                    
                    # If we got here, the extraction was successful
                    break
                    
                except Exception as e:
                    if attempt == max_retries - 1:  # Last attempt failed
                        logger.warning(
                            f"LLM PICO提取或JSON解析失败 (ID: {study_id}): {str(e)}. "
                            f"将使用默认值。响应: {response[:200]}..."
                        )
                        pico_data = default_pico
                    continue
            
            # Calculate evidence metrics
            try:
                quality_score = self._calculate_quality_score(pico_data)
                direction = self._determine_evidence_direction(pico_data.get('finding', ''))
                theme = self._determine_theme(pico_data)
                
                # Extract additional data for visualization
                visualization_data = self._extract_visualization_data(study, pico_data)

                # Create evidence point with visualization data
                evidence_point = EvidencePoint(
                    study_id=study_id,
                    pico=pico_data,
                    finding=pico_data.get('finding', ''),
                    direction=direction,
                    magnitude=quality_score * 0.9,  # Scale down magnitude slightly
                    quality_score=quality_score,
                    theme=theme
                )

                # Add visualization data to evidence point
                evidence_point.visualization_data = visualization_data
                
                logger.info(f"成功从研究 {study_id} 提取证据点: {evidence_point.theme}")
                return [evidence_point]
                
            except Exception as metric_error:
                logger.error(
                    f"计算证据指标时出错 (ID: {study_id}): {str(metric_error)}", 
                    exc_info=True
                )
                return []
            
        except Exception as e:
            logger.error(
                f"处理研究时发生意外错误 (ID: {study_id}): {str(e)}", 
                exc_info=True
            )
            return []
            
    # 这是第一套、逻辑更完整的原始方法，将被程序使用
    def _calculate_quality_score(self, pico_data: Dict[str, str]) -> float:
        score = 0.0
        pico_fields = ['population', 'intervention', 'comparison', 'outcome']
        complete_pico = sum(1 for field in pico_fields if pico_data.get(field) not in ["", "无"])
        score += (complete_pico / 4) * 0.4
        
        study_design = str(pico_data.get('study_design', '')).lower()
        if 'randomized' in study_design or 'rct' in study_design or '随机' in study_design:
            score += 0.3
        elif 'cohort' in study_design or '队列' in study_design:
            score += 0.2
        elif 'case-control' in study_design or '病例对照' in study_design:
            score += 0.15
        elif 'cross-sectional' in study_design or '横断面' in study_design:
            score += 0.1
            
        sample_size = str(pico_data.get('sample_size', ''))
        try:
            numbers = [int(s) for s in re.findall(r'\d+', sample_size)]
            if numbers:
                max_sample = max(numbers)
                if max_sample >= 1000: score += 0.2
                elif max_sample >= 500: score += 0.15
                elif max_sample >= 100: score += 0.1
                else: score += 0.05
        except (ValueError, TypeError): pass
            
        quality_indicators = str(pico_data.get('quality_indicators', '')).lower()
        quality_terms = ['random', 'blind', 'double-blind', 'placebo', 'controlled', '随机', '双盲', '安慰剂', '对照']
        if any(term in quality_indicators for term in quality_terms):
            score += 0.1
            
        return min(max(score, 0.0), 1.0)
        
    def _determine_evidence_direction(self, finding: str) -> EvidenceDirection:
        if not finding or finding == "无":
            return EvidenceDirection.NEUTRAL
        
        finding_lower = finding.lower()
        positive_terms = ['有效', '改善', '提高', '降低', '减少', '优于', '显著', 'positive', 'effective', 'improve', 'increase', 'decrease', 'reduce', 'better', 'superior']
        negative_terms = ['无效', '恶化', '增加', '差于', '无显著', 'negative', 'no effect', 'ineffective', 'worse', 'inferior', 'no difference', 'not significant']
        mixed_terms = ['部分有效', '部分改善', 'mixed', 'partially effective', 'some improvement']
        
        is_positive = any(term in finding_lower for term in positive_terms)
        is_negative = any(term in finding_lower for term in negative_terms)
        is_mixed = any(term in finding_lower for term in mixed_terms)
        
        if is_mixed or (is_positive and is_negative):
            return EvidenceDirection.MIXED
        elif is_positive:
            return EvidenceDirection.POSITIVE
        elif is_negative:
            return EvidenceDirection.NEGATIVE
        else:
            return EvidenceDirection.NEUTRAL
            
    def _determine_theme(self, pico_data: Dict[str, str]) -> str:
        intervention = str(pico_data.get('intervention', '')).lower()
        population = str(pico_data.get('population', '')).lower()
        outcome = str(pico_data.get('outcome', '')).lower()
        
        theme_keywords = {
            "药物治疗": ["药物", "治疗", "疗法", "用药", "medication", "therapy", "treatment"],
            "手术治疗": ["手术", "外科", "surgery", "surgical", "operation"],
            "预防措施": ["预防", "vaccine", "vaccination", "immunization", "immunisation"],
            "诊断方法": ["诊断", "检测", "screening", "diagnosis", "test", "检测"],
            "康复护理": ["康复", "护理", "rehabilitation", "care", "recovery"],
            "生活方式干预": ["饮食", "运动", "锻炼", "lifestyle", "diet", "exercise"],
            "中医治疗": ["中药", "针灸", "推拿", "中医", "herbal", "acupuncture"],
            "并发症管理": ["并发症", "合并症", "complication", "comorbidity"]
        }
        
        text = f"{intervention} {population} {outcome}"
        for theme, keywords in theme_keywords.items():
            if any(keyword in text for keyword in keywords):
                return theme
        return "其他"
    
    def _parse_llm_response(self, response: str) -> Dict[str, str]:
        try:
            response = response.replace('```json', '').replace('```', '').strip()
            return json.loads(response)
        except json.JSONDecodeError:
            return self._extract_key_value_pairs(response)
    
    def _extract_key_value_pairs(self, text: str) -> Dict[str, str]:
        result = {}
        lines = text.split('\n')
        current_key = None
        for line in lines:
            line = line.strip()
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip()
                result[key] = value
                current_key = key
            elif current_key and line:
                result[current_key] += ' ' + line
        return result
    

    def get_theme_summary(self, theme_name: str) -> Optional[Dict]:
        theme = self.themes.get(theme_name)
        return theme.to_dict() if theme else None
    
    def get_overall_summary(self) -> Dict:
        return {
            'total_studies': len(self.study_ids),
            'total_evidence': sum(len(t.evidence_points) for t in self.themes.values()),
            'themes': [t.to_dict() for t in self.themes.values()]
        }

    def _extract_visualization_data(self, study: Dict[str, Any], pico_data: Dict[str, str]) -> Dict[str, Any]:
        """
        提取用于可视化的数据

        Args:
            study: 研究数据
            pico_data: PICO数据

        Returns:
            Dict: 可视化数据
        """
        viz_data = {
            'study_label': f"{study.get('authors', ['Unknown'])[0] if study.get('authors') else 'Unknown'} ({study.get('year', 'N/A')})",
            'effect_size': None,
            'ci_lower': None,
            'ci_upper': None,
            'standard_error': None,
            'sample_size': None,
            'weight': None,
            'risk_of_bias': {}
        }

        # 尝试从多个字段提取样本量
        sample_size_str = pico_data.get('sample_size', '')
        if sample_size_str and sample_size_str != '无':
            try:
                # 提取数字
                numbers = re.findall(r'\d+', sample_size_str)
                if numbers:
                    viz_data['sample_size'] = int(numbers[0])
            except (ValueError, IndexError):
                pass

        # 尝试从摘要或结果中提取效应量信息
        abstract = study.get('abstract', '')
        finding = pico_data.get('finding', '')
        text_to_search = f"{abstract} {finding}".lower()

        # 查找效应量相关信息
        self._extract_effect_size_from_text(text_to_search, viz_data)

        # 评估偏倚风险
        viz_data['risk_of_bias'] = self._assess_risk_of_bias(study, pico_data)

        # 计算权重（基于样本量和研究质量）
        if viz_data['sample_size']:
            base_weight = min(100, viz_data['sample_size'] / 10)  # 基础权重
            quality_multiplier = viz_data.get('quality_score', 0.5)
            viz_data['weight'] = base_weight * quality_multiplier

        return viz_data

    def _extract_effect_size_from_text(self, text: str, viz_data: Dict[str, Any]) -> None:
        """
        从文本中提取效应量信息

        Args:
            text: 要搜索的文本
            viz_data: 可视化数据字典（会被修改）
        """
        # 查找比值比 (OR, odds ratio)
        or_patterns = [
            r'or[:\s=]*(\d+\.?\d*)',
            r'odds\s+ratio[:\s=]*(\d+\.?\d*)',
            r'比值比[:\s=]*(\d+\.?\d*)'
        ]

        for pattern in or_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    viz_data['effect_size'] = float(match.group(1))
                    break
                except ValueError:
                    continue

        # 查找相对风险 (RR, relative risk)
        if viz_data['effect_size'] is None:
            rr_patterns = [
                r'rr[:\s=]*(\d+\.?\d*)',
                r'relative\s+risk[:\s=]*(\d+\.?\d*)',
                r'相对风险[:\s=]*(\d+\.?\d*)'
            ]

            for pattern in rr_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    try:
                        viz_data['effect_size'] = float(match.group(1))
                        break
                    except ValueError:
                        continue

        # 查找置信区间
        ci_patterns = [
            r'95%\s*ci[:\s]*\[?(\d+\.?\d*)[,\s-]+(\d+\.?\d*)\]?',
            r'95%\s*置信区间[:\s]*\[?(\d+\.?\d*)[,\s-]+(\d+\.?\d*)\]?',
            r'\[(\d+\.?\d*)[,\s-]+(\d+\.?\d*)\]'
        ]

        for pattern in ci_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    viz_data['ci_lower'] = float(match.group(1))
                    viz_data['ci_upper'] = float(match.group(2))
                    break
                except ValueError:
                    continue

        # 如果有置信区间但没有效应量，尝试计算
        if viz_data['effect_size'] is None and viz_data['ci_lower'] and viz_data['ci_upper']:
            viz_data['effect_size'] = (viz_data['ci_lower'] + viz_data['ci_upper']) / 2

        # 计算标准误（如果有置信区间）
        if viz_data['ci_lower'] and viz_data['ci_upper'] and viz_data['effect_size']:
            # 假设95%置信区间，标准误 = (上限 - 下限) / (2 * 1.96)
            viz_data['standard_error'] = (viz_data['ci_upper'] - viz_data['ci_lower']) / (2 * 1.96)

    def _assess_risk_of_bias(self, study: Dict[str, Any], pico_data: Dict[str, str]) -> Dict[str, Dict[str, str]]:
        """
        评估研究的偏倚风险

        Args:
            study: 研究数据
            pico_data: PICO数据

        Returns:
            Dict: 偏倚风险评估结果
        """
        rob_assessment = {}

        # 随机化偏倚
        rob_assessment['randomization'] = self._assess_randomization_bias(study, pico_data)

        # 分配隐藏偏倚
        rob_assessment['allocation_concealment'] = self._assess_allocation_bias(study, pico_data)

        # 盲法偏倚
        rob_assessment['blinding'] = self._assess_blinding_bias(study, pico_data)

        # 不完整结果数据偏倚
        rob_assessment['incomplete_outcome'] = self._assess_incomplete_outcome_bias(study, pico_data)

        # 选择性报告偏倚
        rob_assessment['selective_reporting'] = self._assess_selective_reporting_bias(study, pico_data)

        # 其他偏倚
        rob_assessment['other_bias'] = self._assess_other_bias(study, pico_data)

        return rob_assessment

    def _assess_randomization_bias(self, study: Dict[str, Any], pico_data: Dict[str, str]) -> Dict[str, str]:
        """评估随机化偏倚"""
        text = f"{study.get('title', '')} {study.get('abstract', '')} {pico_data.get('study_design', '')}".lower()

        if any(term in text for term in ['randomized', 'randomised', '随机', 'rct']):
            if any(term in text for term in ['computer', 'table', 'sequence', '计算机', '随机数表']):
                return {'risk': 'low', 'description': '明确描述了随机化方法', 'support': '研究中提及了具体的随机化方法'}
            else:
                return {'risk': 'unclear', 'description': '提及随机化但方法不明确', 'support': '研究声称随机化但未详细描述方法'}
        else:
            return {'risk': 'high', 'description': '未提及随机化', 'support': '研究中未发现随机化的证据'}

    def _assess_allocation_bias(self, study: Dict[str, Any], pico_data: Dict[str, str]) -> Dict[str, str]:
        """评估分配隐藏偏倚"""
        text = f"{study.get('title', '')} {study.get('abstract', '')}".lower()

        if any(term in text for term in ['concealed', 'sealed envelope', 'central', '隐藏', '密封信封']):
            return {'risk': 'low', 'description': '描述了分配隐藏方法', 'support': '研究中提及了分配隐藏措施'}
        elif 'randomized' in text or '随机' in text:
            return {'risk': 'unclear', 'description': '随机化研究但分配隐藏不明确', 'support': '研究为随机化设计但未明确描述分配隐藏'}
        else:
            return {'risk': 'high', 'description': '非随机化研究', 'support': '研究设计不涉及分配隐藏'}

    def _assess_blinding_bias(self, study: Dict[str, Any], pico_data: Dict[str, str]) -> Dict[str, str]:
        """评估盲法偏倚"""
        text = f"{study.get('title', '')} {study.get('abstract', '')}".lower()

        if any(term in text for term in ['double-blind', 'double blind', '双盲']):
            return {'risk': 'low', 'description': '双盲设计', 'support': '研究采用双盲设计'}
        elif any(term in text for term in ['single-blind', 'single blind', 'blind', '单盲', '盲法']):
            return {'risk': 'unclear', 'description': '部分盲法设计', 'support': '研究采用部分盲法设计'}
        else:
            return {'risk': 'high', 'description': '开放标签研究', 'support': '研究未采用盲法设计'}

    def _assess_incomplete_outcome_bias(self, study: Dict[str, Any], pico_data: Dict[str, str]) -> Dict[str, str]:
        """评估不完整结果数据偏倚"""
        text = f"{study.get('abstract', '')}".lower()

        if any(term in text for term in ['intention-to-treat', 'itt', '意向性治疗']):
            return {'risk': 'low', 'description': '采用意向性治疗分析', 'support': '研究采用ITT分析处理缺失数据'}
        elif any(term in text for term in ['dropout', 'withdrawal', '脱落', '退出']):
            return {'risk': 'unclear', 'description': '存在脱落但处理方法不明确', 'support': '研究报告了脱落情况但处理方法不清楚'}
        else:
            return {'risk': 'unclear', 'description': '缺失数据处理方法不明确', 'support': '研究未明确说明缺失数据的处理方法'}

    def _assess_selective_reporting_bias(self, study: Dict[str, Any], pico_data: Dict[str, str]) -> Dict[str, str]:
        """评估选择性报告偏倚"""
        # 由于缺乏研究方案信息，通常评估为不明确
        return {'risk': 'unclear', 'description': '无法获得研究方案信息', 'support': '缺乏研究方案，无法评估选择性报告偏倚'}

    def _assess_other_bias(self, study: Dict[str, Any], pico_data: Dict[str, str]) -> Dict[str, str]:
        """评估其他偏倚"""
        # 基于研究设计和资助来源等因素评估
        text = f"{study.get('title', '')} {study.get('abstract', '')}".lower()

        if any(term in text for term in ['industry', 'pharmaceutical', 'company', '制药', '公司资助']):
            return {'risk': 'unclear', 'description': '可能存在利益冲突', 'support': '研究可能受到商业资助'}
        else:
            return {'risk': 'low', 'description': '未发现明显的其他偏倚来源', 'support': '研究未发现明显的其他偏倚风险'}