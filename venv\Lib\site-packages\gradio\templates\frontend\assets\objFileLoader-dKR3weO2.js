import{C as M,T as D,aS as x,ay as F,V as y,aa as C,h as V,aT as P,ab as A,aU as L,aV as T,b as I,af as v}from"./index-SLPGw9aX.js";import{A as U}from"./assetContainer-9eHOOeSf.js";import{S as B}from"./standardMaterial-CYPIoZqG.js";import"./index-Co_Q4qaw.js";import"./svelte/svelte.js";class g{constructor(){this.materials=[]}parseMTL(t,n,e,a){if(n instanceof ArrayBuffer)return;const l=n.split(`
`),h=/\s+/;let _,r=null;for(let c=0;c<l.length;c++){const o=l[c].trim();if(o.length===0||o.charAt(0)==="#")continue;const s=o.indexOf(" ");let i=s>=0?o.substring(0,s):o;i=i.toLowerCase();const p=s>=0?o.substring(s+1).trim():"";if(i==="newmtl")r&&this.materials.push(r),t._blockEntityCollection=!!a,r=new B(p,t),r._parentContainer=a,t._blockEntityCollection=!1;else if(i==="kd"&&r)_=p.split(h,3).map(parseFloat),r.diffuseColor=M.FromArray(_);else if(i==="ka"&&r)_=p.split(h,3).map(parseFloat),r.ambientColor=M.FromArray(_);else if(i==="ks"&&r)_=p.split(h,3).map(parseFloat),r.specularColor=M.FromArray(_);else if(i==="ke"&&r)_=p.split(h,3).map(parseFloat),r.emissiveColor=M.FromArray(_);else if(i==="ns"&&r)r.specularPower=parseFloat(p);else if(i==="d"&&r)r.alpha=parseFloat(p);else if(i==="map_ka"&&r)r.ambientTexture=g._GetTexture(e,p,t);else if(i==="map_kd"&&r)r.diffuseTexture=g._GetTexture(e,p,t);else if(i==="map_ks"&&r)r.specularTexture=g._GetTexture(e,p,t);else if(i!=="map_ns")if(i==="map_bump"&&r){const d=p.split(h),f=d.indexOf("-bm");let b=null;f>=0&&(b=d[f+1],d.splice(f,2)),r.bumpTexture=g._GetTexture(e,d.join(" "),t),r.bumpTexture&&b!==null&&(r.bumpTexture.level=parseFloat(b))}else i==="map_d"&&r&&(r.opacityTexture=g._GetTexture(e,p,t))}r&&this.materials.push(r)}static _GetTexture(t,n,e){if(!n)return null;let a=t;if(t==="file:"){let l=n.lastIndexOf("\\");l===-1&&(l=n.lastIndexOf("/")),l>-1?a+=n.substring(l+1):a+=n}else a+=n;return new D(a,e,!1,g.INVERT_TEXTURE_Y)}}g.INVERT_TEXTURE_Y=!0;class u{constructor(t,n,e){this._positions=[],this._normals=[],this._uvs=[],this._colors=[],this._extColors=[],this._meshesFromObj=[],this._indicesForBabylon=[],this._wrappedPositionForBabylon=[],this._wrappedUvsForBabylon=[],this._wrappedColorsForBabylon=[],this._wrappedNormalsForBabylon=[],this._tuplePosNorm=[],this._curPositionInIndices=0,this._hasMeshes=!1,this._unwrappedPositionsForBabylon=[],this._unwrappedColorsForBabylon=[],this._unwrappedNormalsForBabylon=[],this._unwrappedUVForBabylon=[],this._triangles=[],this._materialNameFromObj="",this._objMeshName="",this._increment=1,this._isFirstMaterial=!0,this._grayColor=new x(.5,.5,.5,1),this._hasLineData=!1,this._materialToUse=t,this._babylonMeshesArray=n,this._loadingOptions=e}_isInArray(t,n){t[n[0]]||(t[n[0]]={normals:[],idx:[]});const e=t[n[0]].normals.indexOf(n[1]);return e===-1?-1:t[n[0]].idx[e]}_isInArrayUV(t,n){t[n[0]]||(t[n[0]]={normals:[],idx:[],uv:[]});const e=t[n[0]].normals.indexOf(n[1]);return e!=1&&n[2]===t[n[0]].uv[e]?t[n[0]].idx[e]:-1}_setData(t,n,e,a,l,h,_){let r;this._loadingOptions.optimizeWithUV?r=this._isInArrayUV(this._tuplePosNorm,[t,e,n]):r=this._isInArray(this._tuplePosNorm,[t,e]),r===-1?(this._indicesForBabylon.push(this._wrappedPositionForBabylon.length),this._wrappedPositionForBabylon.push(a),l=l??new F(0,0),this._wrappedUvsForBabylon.push(l),this._wrappedNormalsForBabylon.push(h),_!==void 0&&this._wrappedColorsForBabylon.push(_),this._tuplePosNorm[t].normals.push(e),this._tuplePosNorm[t].idx.push(this._curPositionInIndices++),this._loadingOptions.optimizeWithUV&&this._tuplePosNorm[t].uv.push(n)):this._indicesForBabylon.push(r)}_unwrapData(){try{for(let t=0;t<this._wrappedPositionForBabylon.length;t++)this._unwrappedPositionsForBabylon.push(this._wrappedPositionForBabylon[t].x*this._handednessSign,this._wrappedPositionForBabylon[t].y,this._wrappedPositionForBabylon[t].z),this._unwrappedNormalsForBabylon.push(this._wrappedNormalsForBabylon[t].x*this._handednessSign,this._wrappedNormalsForBabylon[t].y,this._wrappedNormalsForBabylon[t].z),this._unwrappedUVForBabylon.push(this._wrappedUvsForBabylon[t].x,this._wrappedUvsForBabylon[t].y),this._loadingOptions.importVertexColors&&this._unwrappedColorsForBabylon.push(this._wrappedColorsForBabylon[t].r,this._wrappedColorsForBabylon[t].g,this._wrappedColorsForBabylon[t].b,this._wrappedColorsForBabylon[t].a);this._wrappedPositionForBabylon.length=0,this._wrappedNormalsForBabylon.length=0,this._wrappedUvsForBabylon.length=0,this._wrappedColorsForBabylon.length=0,this._tuplePosNorm.length=0,this._curPositionInIndices=0}catch{throw new Error("Unable to unwrap data while parsing OBJ data.")}}_getTriangles(t,n){for(let e=n;e<t.length-1;e++)this._pushTriangle(t,e)}_getColor(t){if(this._loadingOptions.importVertexColors)return this._extColors[t]??this._colors[t]}_setDataForCurrentFaceWithPattern1(t,n){this._getTriangles(t,n);for(let e=0;e<this._triangles.length;e++){const a=parseInt(this._triangles[e])-1;this._setData(a,0,0,this._positions[a],F.Zero(),y.Up(),this._getColor(a))}this._triangles.length=0}_setDataForCurrentFaceWithPattern2(t,n){this._getTriangles(t,n);for(let e=0;e<this._triangles.length;e++){const a=this._triangles[e].split("/"),l=parseInt(a[0])-1,h=parseInt(a[1])-1;this._setData(l,h,0,this._positions[l],this._uvs[h]??F.Zero(),y.Up(),this._getColor(l))}this._triangles.length=0}_setDataForCurrentFaceWithPattern3(t,n){this._getTriangles(t,n);for(let e=0;e<this._triangles.length;e++){const a=this._triangles[e].split("/"),l=parseInt(a[0])-1,h=parseInt(a[1])-1,_=parseInt(a[2])-1;this._setData(l,h,_,this._positions[l],this._uvs[h]??F.Zero(),this._normals[_]??y.Up())}this._triangles.length=0}_setDataForCurrentFaceWithPattern4(t,n){this._getTriangles(t,n);for(let e=0;e<this._triangles.length;e++){const a=this._triangles[e].split("//"),l=parseInt(a[0])-1,h=parseInt(a[1])-1;this._setData(l,1,h,this._positions[l],F.Zero(),this._normals[h],this._getColor(l))}this._triangles.length=0}_setDataForCurrentFaceWithPattern5(t,n){this._getTriangles(t,n);for(let e=0;e<this._triangles.length;e++){const a=this._triangles[e].split("/"),l=this._positions.length+parseInt(a[0]),h=this._uvs.length+parseInt(a[1]),_=this._normals.length+parseInt(a[2]);this._setData(l,h,_,this._positions[l],this._uvs[h],this._normals[_],this._getColor(l))}this._triangles.length=0}_addPreviousObjMesh(){this._meshesFromObj.length>0&&(this._handledMesh=this._meshesFromObj[this._meshesFromObj.length-1],this._unwrapData(),this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._handledMesh.indices=this._indicesForBabylon.slice(),this._handledMesh.positions=this._unwrappedPositionsForBabylon.slice(),this._handledMesh.normals=this._unwrappedNormalsForBabylon.slice(),this._handledMesh.uvs=this._unwrappedUVForBabylon.slice(),this._handledMesh.hasLines=this._hasLineData,this._loadingOptions.importVertexColors&&(this._handledMesh.colors=this._unwrappedColorsForBabylon.slice()),this._indicesForBabylon.length=0,this._unwrappedPositionsForBabylon.length=0,this._unwrappedColorsForBabylon.length=0,this._unwrappedNormalsForBabylon.length=0,this._unwrappedUVForBabylon.length=0,this._hasLineData=!1)}_optimizeNormals(t){const n=t.getVerticesData(C.PositionKind),e=t.getVerticesData(C.NormalKind),a={};if(!n||!e)return;for(let h=0;h<n.length/3;h++){const _=n[h*3+0],r=n[h*3+1],c=n[h*3+2],o=_+"_"+r+"_"+c;let s=a[o];s||(s=[],a[o]=s),s.push(h)}const l=new y;for(const h in a){const _=a[h];if(_.length<2)continue;const r=_[0];for(let c=1;c<_.length;++c){const o=_[c];e[r*3+0]+=e[o*3+0],e[r*3+1]+=e[o*3+1],e[r*3+2]+=e[o*3+2]}l.copyFromFloats(e[r*3+0],e[r*3+1],e[r*3+2]),l.normalize();for(let c=0;c<_.length;++c){const o=_[c];e[o*3+0]=l.x,e[o*3+1]=l.y,e[o*3+2]=l.z}}t.setVerticesData(C.NormalKind,e)}static _IsLineElement(t){return t.startsWith("l")}static _IsObjectElement(t){return t.startsWith("o")}static _IsGroupElement(t){return t.startsWith("g")}static _GetZbrushMRGB(t,n){if(!t.startsWith("mrgb"))return null;if(t=t.replace("mrgb","").trim(),n)return[];const e=/[a-z0-9]/g,a=t.match(e);if(!a||a.length%8!==0)return[];const l=[];for(let h=0;h<a.length/8;h++){const _=a[h*8+2]+a[h*8+3],r=a[h*8+4]+a[h*8+5],c=a[h*8+6]+a[h*8+7];l.push(new x(parseInt(_,16)/255,parseInt(r,16)/255,parseInt(c,16)/255,1))}return l}parse(t,n,e,a,l){n=n.replace(/#MRGB/g,"mrgb"),n=n.replace(/#.*$/gm,"").trim(),this._loadingOptions.useLegacyBehavior?(this._pushTriangle=(o,s)=>this._triangles.push(o[0],o[s],o[s+1]),this._handednessSign=1):e.useRightHandedSystem?(this._pushTriangle=(o,s)=>this._triangles.push(o[0],o[s+1],o[s]),this._handednessSign=1):(this._pushTriangle=(o,s)=>this._triangles.push(o[0],o[s],o[s+1]),this._handednessSign=-1);const h=n.split(`
`),_=[];let r=[];_.push(r);for(let o=0;o<h.length;o++){const s=h[o].trim().replace(/\s\s/g," ");if(!(s.length===0||s.charAt(0)==="#"))if((u._IsGroupElement(s)||u._IsObjectElement(s))&&(r=[],_.push(r)),u._IsLineElement(s)){const i=s.split(" ");for(let p=1;p<i.length-1;p++)r.push(`l ${i[p]} ${i[p+1]}`)}else r.push(s)}const c=_.flat();for(let o=0;o<c.length;o++){const s=c[o].trim().replace(/\s\s/g," ");let i;if(!(s.length===0||s.charAt(0)==="#"))if(u.VertexPattern.test(s)){if(i=s.match(/[^ ]+/g),this._positions.push(new y(parseFloat(i[1]),parseFloat(i[2]),parseFloat(i[3]))),this._loadingOptions.importVertexColors)if(i.length>=7){const p=parseFloat(i[4]),d=parseFloat(i[5]),f=parseFloat(i[6]);this._colors.push(new x(p>1?p/255:p,d>1?d/255:d,f>1?f/255:f,i.length===7||i[7]===void 0?1:parseFloat(i[7])))}else this._colors.push(this._grayColor)}else if((i=u.NormalPattern.exec(s))!==null)this._normals.push(new y(parseFloat(i[1]),parseFloat(i[2]),parseFloat(i[3])));else if((i=u.UVPattern.exec(s))!==null)this._uvs.push(new F(parseFloat(i[1])*this._loadingOptions.UVScaling.x,parseFloat(i[2])*this._loadingOptions.UVScaling.y));else if((i=u.FacePattern3.exec(s))!==null)this._setDataForCurrentFaceWithPattern3(i[1].trim().split(" "),1);else if((i=u.FacePattern4.exec(s))!==null)this._setDataForCurrentFaceWithPattern4(i[1].trim().split(" "),1);else if((i=u.FacePattern5.exec(s))!==null)this._setDataForCurrentFaceWithPattern5(i[1].trim().split(" "),1);else if((i=u.FacePattern2.exec(s))!==null)this._setDataForCurrentFaceWithPattern2(i[1].trim().split(" "),1);else if((i=u.FacePattern1.exec(s))!==null)this._setDataForCurrentFaceWithPattern1(i[1].trim().split(" "),1);else if((i=u.LinePattern1.exec(s))!==null)this._setDataForCurrentFaceWithPattern1(i[1].trim().split(" "),0),this._hasLineData=!0;else if((i=u.LinePattern2.exec(s))!==null)this._setDataForCurrentFaceWithPattern2(i[1].trim().split(" "),0),this._hasLineData=!0;else if(i=u._GetZbrushMRGB(s,!this._loadingOptions.importVertexColors))i.forEach(p=>{this._extColors.push(p)});else if((i=u.LinePattern3.exec(s))!==null)this._setDataForCurrentFaceWithPattern3(i[1].trim().split(" "),0),this._hasLineData=!0;else if(u.GroupDescriptor.test(s)||u.ObjectDescriptor.test(s)){const p={name:s.substring(2).trim(),indices:null,positions:null,normals:null,uvs:null,colors:null,materialName:this._materialNameFromObj,isObject:u.ObjectDescriptor.test(s)};this._addPreviousObjMesh(),this._meshesFromObj.push(p),this._hasMeshes=!0,this._isFirstMaterial=!0,this._increment=1}else if(u.UseMtlDescriptor.test(s)){if(this._materialNameFromObj=s.substring(7).trim(),!this._isFirstMaterial||!this._hasMeshes){this._addPreviousObjMesh();const p={name:(this._objMeshName||"mesh")+"_mm"+this._increment.toString(),indices:null,positions:null,normals:null,uvs:null,colors:null,materialName:this._materialNameFromObj,isObject:!1};this._increment++,this._meshesFromObj.push(p),this._hasMeshes=!0}this._hasMeshes&&this._isFirstMaterial&&(this._meshesFromObj[this._meshesFromObj.length-1].materialName=this._materialNameFromObj,this._isFirstMaterial=!1)}else u.MtlLibGroupDescriptor.test(s)?l(s.substring(7).trim()):u.SmoothDescriptor.test(s)||V.Log("Unhandled expression at line : "+s)}if(this._hasMeshes&&(this._handledMesh=this._meshesFromObj[this._meshesFromObj.length-1],this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._unwrapData(),this._handledMesh.indices=this._indicesForBabylon,this._handledMesh.positions=this._unwrappedPositionsForBabylon,this._handledMesh.normals=this._unwrappedNormalsForBabylon,this._handledMesh.uvs=this._unwrappedUVForBabylon,this._handledMesh.hasLines=this._hasLineData,this._loadingOptions.importVertexColors&&(this._handledMesh.colors=this._unwrappedColorsForBabylon)),!this._hasMeshes){let o=null;if(this._indicesForBabylon.length)this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._unwrapData();else{for(const s of this._positions)this._unwrappedPositionsForBabylon.push(s.x,s.y,s.z);if(this._normals.length)for(const s of this._normals)this._unwrappedNormalsForBabylon.push(s.x,s.y,s.z);if(this._uvs.length)for(const s of this._uvs)this._unwrappedUVForBabylon.push(s.x,s.y);if(this._extColors.length)for(const s of this._extColors)this._unwrappedColorsForBabylon.push(s.r,s.g,s.b,s.a);else if(this._colors.length)for(const s of this._colors)this._unwrappedColorsForBabylon.push(s.r,s.g,s.b,s.a);this._materialNameFromObj||(o=new B(P.RandomId(),e),o.pointsCloud=!0,this._materialNameFromObj=o.name,this._normals.length||(o.disableLighting=!0,o.emissiveColor=M.White()))}this._meshesFromObj.push({name:P.RandomId(),indices:this._indicesForBabylon,positions:this._unwrappedPositionsForBabylon,colors:this._unwrappedColorsForBabylon,normals:this._unwrappedNormalsForBabylon,uvs:this._unwrappedUVForBabylon,materialName:this._materialNameFromObj,directMaterial:o,isObject:!0,hasLines:this._hasLineData})}for(let o=0;o<this._meshesFromObj.length;o++){if(t&&this._meshesFromObj[o].name){if(t instanceof Array){if(t.indexOf(this._meshesFromObj[o].name)===-1)continue}else if(this._meshesFromObj[o].name!==t)continue}this._handledMesh=this._meshesFromObj[o],e._blockEntityCollection=!!a;const s=new A(this._meshesFromObj[o].name,e);if(s._parentContainer=a,e._blockEntityCollection=!1,this._handledMesh._babylonMesh=s,!this._handledMesh.isObject){for(let p=o-1;p>=0;--p)if(this._meshesFromObj[p].isObject&&this._meshesFromObj[p]._babylonMesh){s.parent=this._meshesFromObj[p]._babylonMesh;break}}if(this._materialToUse.push(this._meshesFromObj[o].materialName),this._handledMesh.hasLines&&(s._internalMetadata??(s._internalMetadata={}),s._internalMetadata._isLine=!0),this._handledMesh.positions?.length===0){this._babylonMeshesArray.push(s);continue}const i=new L;if(i.uvs=this._handledMesh.uvs,i.indices=this._handledMesh.indices,i.positions=this._handledMesh.positions,this._loadingOptions.computeNormals){const p=new Array;L.ComputeNormals(this._handledMesh.positions,this._handledMesh.indices,p),i.normals=p}else i.normals=this._handledMesh.normals;this._loadingOptions.importVertexColors&&(i.colors=this._handledMesh.colors),i.applyToMesh(s),this._loadingOptions.invertY&&(s.scaling.y*=-1),this._loadingOptions.optimizeNormals&&this._optimizeNormals(s),this._babylonMeshesArray.push(s),this._handledMesh.directMaterial&&(s.material=this._handledMesh.directMaterial)}}}u.ObjectDescriptor=/^o/;u.GroupDescriptor=/^g/;u.MtlLibGroupDescriptor=/^mtllib /;u.UseMtlDescriptor=/^usemtl /;u.SmoothDescriptor=/^s /;u.VertexPattern=/^v(\s+[\d|.|+|\-|e|E]+){3,7}/;u.NormalPattern=/^vn(\s+[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)/;u.UVPattern=/^vt(\s+[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)/;u.FacePattern1=/^f\s+(([\d]{1,}[\s]?){3,})+/;u.FacePattern2=/^f\s+((([\d]{1,}\/[\d]{1,}[\s]?){3,})+)/;u.FacePattern3=/^f\s+((([\d]{1,}\/[\d]{1,}\/[\d]{1,}[\s]?){3,})+)/;u.FacePattern4=/^f\s+((([\d]{1,}\/\/[\d]{1,}[\s]?){3,})+)/;u.FacePattern5=/^f\s+(((-[\d]{1,}\/-[\d]{1,}\/-[\d]{1,}[\s]?){3,})+)/;u.LinePattern1=/^l\s+(([\d]{1,}[\s]?){2,})+/;u.LinePattern2=/^l\s+((([\d]{1,}\/[\d]{1,}[\s]?){2,})+)/;u.LinePattern3=/^l\s+((([\d]{1,}\/[\d]{1,}\/[\d]{1,}[\s]?){2,})+)/;class m{static get INVERT_TEXTURE_Y(){return g.INVERT_TEXTURE_Y}static set INVERT_TEXTURE_Y(t){g.INVERT_TEXTURE_Y=t}constructor(t){this.name=T.name,this.extensions=T.extensions,this._assetContainer=null,this._loadingOptions={...m._DefaultLoadingOptions,...t??{}}}static get _DefaultLoadingOptions(){return{computeNormals:m.COMPUTE_NORMALS,optimizeNormals:m.OPTIMIZE_NORMALS,importVertexColors:m.IMPORT_VERTEX_COLORS,invertY:m.INVERT_Y,invertTextureY:m.INVERT_TEXTURE_Y,UVScaling:m.UV_SCALING,materialLoadingFailsSilently:m.MATERIAL_LOADING_FAILS_SILENTLY,optimizeWithUV:m.OPTIMIZE_WITH_UV,skipMaterials:m.SKIP_MATERIALS,useLegacyBehavior:m.USE_LEGACY_BEHAVIOR}}_loadMTL(t,n,e,a){const l=n+t;I.LoadFile(l,e,void 0,void 0,!1,(h,_)=>{a(l,_)})}createPlugin(t){return new m(t[T.name])}canDirectLoad(){return!1}importMeshAsync(t,n,e,a){return this._parseSolid(t,n,e,a).then(l=>({meshes:l,particleSystems:[],skeletons:[],animationGroups:[],transformNodes:[],geometries:[],lights:[],spriteManagers:[]}))}loadAsync(t,n,e){return this.importMeshAsync(null,t,n,e).then(()=>{})}loadAssetContainerAsync(t,n,e){const a=new U(t);return this._assetContainer=a,this.importMeshAsync(null,t,n,e).then(l=>(l.meshes.forEach(h=>a.meshes.push(h)),l.meshes.forEach(h=>{const _=h.material;_&&a.materials.indexOf(_)==-1&&(a.materials.push(_),_.getActiveTextures().forEach(c=>{a.textures.indexOf(c)==-1&&a.textures.push(c)}))}),this._assetContainer=null,a)).catch(l=>{throw this._assetContainer=null,l})}_parseSolid(t,n,e,a){let l="";const h=new g,_=[],r=[];e=e.replace(/#.*$/gm,"").trim(),new u(_,r,this._loadingOptions).parse(t,e,n,this._assetContainer,s=>{l=s});const o=[];return l!==""&&!this._loadingOptions.skipMaterials&&o.push(new Promise((s,i)=>{this._loadMTL(l,a,p=>{try{h.parseMTL(n,p,a,this._assetContainer);for(let d=0;d<h.materials.length;d++){let f=0;const b=[];let w;for(;(w=_.indexOf(h.materials[d].name,f))>-1;)b.push(w),f=w+1;if(w===-1&&b.length===0)h.materials[d].dispose();else for(let O=0;O<b.length;O++){const E=r[b[O]],N=h.materials[d];E.material=N,E.getTotalIndices()||(N.pointsCloud=!0)}}s()}catch(d){I.Warn(`Error processing MTL file: '${l}'`),this._loadingOptions.materialLoadingFailsSilently?s():i(d)}},(p,d)=>{I.Warn(`Error downloading MTL file: '${l}'`),this._loadingOptions.materialLoadingFailsSilently?s():i(d)})})),Promise.all(o).then(()=>{const s=i=>!!(i._internalMetadata?._isLine??!1);return r.forEach(i=>{if(s(i)){let p=i.material??new B(i.name+"_line",n);p.getBindedMeshes().filter(f=>!s(f)).length>0&&(p=p.clone(p.name+"_line")??p),p.wireframe=!0,i.material=p,i._internalMetadata&&(i._internalMetadata._isLine=void 0)}}),r})}}m.OPTIMIZE_WITH_UV=!0;m.INVERT_Y=!1;m.IMPORT_VERTEX_COLORS=!1;m.COMPUTE_NORMALS=!1;m.OPTIMIZE_NORMALS=!1;m.UV_SCALING=new F(1,1);m.SKIP_MATERIALS=!1;m.MATERIAL_LOADING_FAILS_SILENTLY=!0;m.USE_LEGACY_BEHAVIOR=!1;v(new m);export{m as OBJFileLoader};
//# sourceMappingURL=objFileLoader-dKR3weO2.js.map
