"""
偏倚风险图实现
用于展示纳入研究的偏倚风险评估
"""
from typing import Dict, Any, List, Optional
import numpy as np
import plotly.graph_objects as go
from .base_plot import BasePlot

class RobPlot(BasePlot):
    """
    偏倚风险图类，用于展示研究的偏倚风险评估
    """
    
    def _create_figure(self) -> go.Figure:
        """
        创建偏倚风险图
        
        Returns:
            go.Figure: 偏倚风险图对象
        """
        fig = go.Figure()
        
        # 提取数据
        studies = self.data.get('studies', [])
        if not studies:
            return self._create_empty_plot()
        
        # 获取所有评估领域
        domains = self._get_domains(studies)
        if not domains:
            return self._create_empty_plot('没有找到评估领域')
        
        # 创建热图数据
        self._create_heatmap(fig, studies, domains)
        
        # 更新布局
        self._update_layout(fig, domains)
        
        return fig
    
    def _get_domains(self, studies: List[Dict[str, Any]]) -> List[str]:
        """
        从研究中提取所有评估领域
        
        Args:
            studies: 研究数据列表
            
        Returns:
            List[str]: 评估领域列表
        """
        domains = set()
        for study in studies:
            if 'risk_of_bias' in study and isinstance(study['risk_of_bias'], dict):
                domains.update(study['risk_of_bias'].keys())
        return sorted(domains)
    
    def _create_heatmap(self, fig: go.Figure, studies: List[Dict[str, Any]], domains: List[str]) -> None:
        """
        创建热图
        
        Args:
            fig: Plotly图表对象
            studies: 研究数据列表
            domains: 评估领域列表
        """
        # 准备数据
        study_labels = [study.get('label', f'研究 {i+1}') for i, study in enumerate(studies)]
        
        # 创建颜色映射
        color_map = {
            'low': '#4CAF50',    # 绿色 - 低风险
            'unclear': '#FFC107', # 黄色 - 不清楚
            'high': '#F44336'     # 红色 - 高风险
        }
        
        # 创建热图数据
        z = []
        customdata = []
        
        for study in studies:
            row = []
            data_row = []
            rob = study.get('risk_of_bias', {})
            
            for domain in domains:
                domain_rob = rob.get(domain, {})
                risk = domain_rob.get('risk', 'unclear').lower()
                
                # 映射到数值用于热图
                if risk == 'low':
                    row.append(0)
                elif risk == 'unclear':
                    row.append(1)
                else:  # high
                    row.append(2)
                
                # 保存自定义数据用于悬停
                data_row.append([
                    risk.capitalize(),
                    domain_rob.get('description', '无描述'),
                    domain_rob.get('support', '无支持信息')
                ])
            
            z.append(row)
            customdata.append(data_row)
        
        # 转置矩阵，使领域在Y轴，研究在X轴
        z = list(zip(*z))
        customdata = list(zip(*customdata))
        
        # 添加热图
        fig.add_trace(go.Heatmap(
            z=z,
            x=study_labels,
            y=domains,
            colorscale=[color_map['low'], color_map['unclear'], color_map['high']],
            zmin=0,
            zmax=2,
            showscale=False,
            hoverongaps=False,
            customdata=customdata,
            hovertemplate=(
                "<b>%{y}</b><br>"
                "<b>%{x}</b><br>"
                "风险: <b>%{customdata[0]}</b><br>"
                "描述: %{customdata[1]}<br>"
                "支持: %{customdata[2]}<br>"
                "<extra></extra>"
            )
        ))
        
        # 添加颜色图例
        for i, (label, color) in enumerate([
            ('低风险', color_map['low']),
            ('不清楚', color_map['unclear']),
            ('高风险', color_map['high'])
        ]):
            fig.add_trace(go.Scatter(
                x=[None],  # 不在图表中显示
                y=[None],
                mode='markers',
                marker=dict(size=10, color=color, symbol='square'),
                name=label,
                showlegend=True
            ))
    
    def _update_layout(self, fig: go.Figure, domains: List[str]) -> None:
        """
        更新图表布局
        
        Args:
            fig: Plotly图表对象
            domains: 评估领域列表
        """
        # 更新布局
        fig.update_layout(
            title=dict(
                text='<b>偏倚风险评估</b>',
                x=0.5,
                xanchor='center',
                y=0.95,
                yanchor='top'
            ),
            xaxis=dict(
                title='<b>研究</b>',
                showgrid=False,
                showline=True,
                linewidth=1,
                linecolor='black',
                mirror=True,
                tickangle=-45,
                side='top'
            ),
            yaxis=dict(
                title='<b>评估领域</b>',
                showgrid=False,
                showline=True,
                linewidth=1,
                linecolor='black',
                mirror=True,
                autorange='reversed'  # 反转Y轴
            ),
            legend=dict(
                orientation='h',
                yanchor='bottom',
                y=1.02,
                xanchor='right',
                x=1
            ),
            plot_bgcolor='white',
            margin=dict(l=200, r=50, t=100, b=150),
            height=max(400, 100 * len(domains)),
            width=max(800, 100 * len(fig.data[0].x) if fig.data else 800)
        )
    
    def _create_empty_plot(self, message: str = "没有可用的研究数据") -> go.Figure:
        """
        创建空图表
        
        Args:
            message: 显示的消息
            
        Returns:
            go.Figure: 空图表对象
        """
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white'
        )
        return fig
