../../Scripts/bio.exe,sha256=I8DRM3UEftEhHM9vpCzJI_IZvc9xoLepWNBY0YYj5_Y,108383
../../Scripts/fasta_filter.exe,sha256=AnpnnYyVtoGOR7FTSV3SnqGnr9bM0RGiyA0ir4qDWc8,108395
bio-1.8.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bio-1.8.0.dist-info/METADATA,sha256=6WJQn5r12az2eeRSMmS89ljkN12Kh7802sHuwngTPj0,5683
bio-1.8.0.dist-info/RECORD,,
bio-1.8.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bio-1.8.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
bio-1.8.0.dist-info/entry_points.txt,sha256=IYOkeg4W7ny7-rJ1TbBfSBhV9uZzF1ijR3c7GaDvm0s,94
bio-1.8.0.dist-info/licenses/LICENSE,sha256=WDIr4SDHLOrG6ZmkFf_10FFIGvyJNi5_ewgWr7gpaec,1070
biorun/__about__.py,sha256=Y8ge9-D8_7hE1_PnKsn9BNr5XRRC2R6mPXoDp-YQjxM,134
biorun/__init__.py,sha256=jk3DZug07lIe5-E0XNaCWWHRZD24l10ZzXJ3E2b0nPk,710
biorun/__main__.py,sha256=zb5ikfWTCdTWf2nS2lo5fKXWAUNUPv7GYIYHOjOkpQs,250
biorun/__pycache__/__about__.cpython-310.pyc,,
biorun/__pycache__/__init__.cpython-310.pyc,,
biorun/__pycache__/__main__.cpython-310.pyc,,
biorun/__pycache__/align.cpython-310.pyc,,
biorun/__pycache__/code.cpython-310.pyc,,
biorun/__pycache__/combine.cpython-310.pyc,,
biorun/__pycache__/comm.cpython-310.pyc,,
biorun/__pycache__/convert.cpython-310.pyc,,
biorun/__pycache__/enrichr.cpython-310.pyc,,
biorun/__pycache__/fasta.cpython-310.pyc,,
biorun/__pycache__/fetch.cpython-310.pyc,,
biorun/__pycache__/format.cpython-310.pyc,,
biorun/__pycache__/gff.cpython-310.pyc,,
biorun/__pycache__/gprof.cpython-310.pyc,,
biorun/__pycache__/gtf.cpython-310.pyc,,
biorun/__pycache__/jsonrec.cpython-310.pyc,,
biorun/__pycache__/main.cpython-310.pyc,,
biorun/__pycache__/mart.cpython-310.pyc,,
biorun/__pycache__/meta.cpython-310.pyc,,
biorun/__pycache__/models.cpython-310.pyc,,
biorun/__pycache__/mygene.cpython-310.pyc,,
biorun/__pycache__/ontology.cpython-310.pyc,,
biorun/__pycache__/parser.cpython-310.pyc,,
biorun/__pycache__/patterns.cpython-310.pyc,,
biorun/__pycache__/search.cpython-310.pyc,,
biorun/__pycache__/search2.cpython-310.pyc,,
biorun/__pycache__/table.cpython-310.pyc,,
biorun/__pycache__/taxon.cpython-310.pyc,,
biorun/__pycache__/test.cpython-310.pyc,,
biorun/__pycache__/uniq.cpython-310.pyc,,
biorun/__pycache__/utils.cpython-310.pyc,,
biorun/__pycache__/variant.cpython-310.pyc,,
biorun/__pycache__/vcf2fasta.cpython-310.pyc,,
biorun/align.py,sha256=rSruQzuQVqOCagBhsUA4nOCdrSTu6gJEpfMquhjgZFk,7599
biorun/api/__init__.py,sha256=QW_YfvKnUTfCe1pHiFNLXJlKd28XH5hea8iq-G7j0Vs,54
biorun/api/__pycache__/__init__.cpython-310.pyc,,
biorun/api/__pycache__/ena.cpython-310.pyc,,
biorun/api/__pycache__/ena_fastq.cpython-310.pyc,,
biorun/api/__pycache__/ncbi_datasets.cpython-310.pyc,,
biorun/api/ena.py,sha256=jwsxX1IdB3BYE7N8cgvOqhRKM8SpsxFyOZUxcxnReuw,713
biorun/api/ena_fastq.py,sha256=qCaeQv6CwJTOkybJQ5SGoNFmqqFyz0Bvlg5lqw22IDc,1842
biorun/api/ncbi_datasets.py,sha256=VjfPr9l8pqM-xDq8020qGMymmktP4FNtl-18y3JpdG8,161
biorun/code.py,sha256=i7z99ZQPjcsOagfns4u1UNNTTSh0lVr-Y2KfvSGv1bI,1679
biorun/combine.py,sha256=3z2OOt3YdirRyPwpH6HvCD79Gr-atwpwn0tCmpSo88A,2533
biorun/comm.py,sha256=4_uIJpqdRhvzuuQFISgbW33Ggpsr1QJXCtBI4v4KxfM,3403
biorun/convert.py,sha256=k0K_BXlWmoiR-PHDlXFwxzcdKe3-RVcGEYcgc0hfCYo,10913
biorun/data/BLASTN,sha256=FDsYSTNtohlCBTousptte2V2h0drppXEKhu1ckAL_AU,187
biorun/data/alias.txt,sha256=FdOPpHQrccdL7GgzjjmeC7n0YcVjhvNAQi_Kt1JSMUw,119
biorun/data/align_default.diff,sha256=0Ix4sAx6k41zvWQmd4aPaJdvuULWJpmUSb0G6GOZUWE,52
biorun/data/align_default.txt,sha256=hHeYc6o07lDi31xhdUflFDhfqJZpbEXs5W5P76-Mt70,163
biorun/data/align_default.vcf,sha256=BsqZQ67NiogUtFlNCEy8TXe2bT7Uscle88ldNLt0RCQ,383
biorun/data/align_global.txt,sha256=nX8KRVqOv3kv-QKClPkAmeuIqgz29N0qWMBIw6N3OYU,160
biorun/data/align_input.fa,sha256=d5-v4Kq0Os8ewih0nV3xPlahV1yuJUspt_Y3niPWVpI,395
biorun/data/align_input.vcf,sha256=zsmC7-inlrqfR8fS-wxeMayzQQt2kv9ELqbSoLlGEhE,762
biorun/data/align_local.txt,sha256=-mv6hWWwoJOxEZn6NF1sCS7Tz1HwJP2yVrqsVS1JOlY,147
biorun/data/align_pile1.txt,sha256=1uyeKUB-MQtYOve9gIRG4dkPRRTtcSCpbYg7cJ6tvbo,73
biorun/data/align_s.tsv,sha256=pMA4BPOaR4BVgpRTzVm69VVOXFulJwCl7APl0zTDbeE,91
biorun/data/align_s.txt,sha256=NDCK5lp96JubH_wrXjnl5bqBiC9rhHqai4B-kgiBSfQ,4050
biorun/data/align_s.vcf,sha256=9lDwnc7r3dvq5gmUO9BMqeMPi-1vA_JVKnOwQqt75b4,2008
biorun/data/comm0.txt,sha256=4YS65M57S_GJ7btGSnkZb-AF6bvmd_34OSfO41nWVp0,4
biorun/data/comm1.txt,sha256=wM3nf6j--X1HbBCq09LVT8wvM2FA0HNlHC3Mzx43n9Y,2
biorun/data/comm2.txt,sha256=fER6olJCZKPiTfc6b93Y2zYIQPiVvLXlTWQ8GN4mqK4,2
biorun/data/explain_exon.txt,sha256=u-ML2RYvik_pVQaGJdh-9d-X1ke6ZlFJIEc3HTHyVLM,379
biorun/data/explain_food.txt,sha256=gdFu0G8KCkjiVebVM2pEEc25Xqfh5t8WzpCJAsyaxTI,191
biorun/data/explain_neutral.txt,sha256=j-0nNwjkEJUbGEnYEjyI3Sy2uJlGwvSLkY7arkVtlAg,1208
biorun/data/fasta_alias1.fa,sha256=19J0zpcniKsckQFBtpPTFf0pBmT_2xPMPn5OEn9sz60,405
biorun/data/fasta_all1.fa,sha256=KbTSHrGv7Mvrcwkwgc7MuwkFMPB6xc_UfvrMpZnXbqQ,8297
biorun/data/fasta_all2.fa,sha256=KbTSHrGv7Mvrcwkwgc7MuwkFMPB6xc_UfvrMpZnXbqQ,8297
biorun/data/fasta_all3.fa,sha256=KbTSHrGv7Mvrcwkwgc7MuwkFMPB6xc_UfvrMpZnXbqQ,8297
biorun/data/fasta_all4.fa,sha256=KbTSHrGv7Mvrcwkwgc7MuwkFMPB6xc_UfvrMpZnXbqQ,8297
biorun/data/fasta_cds.fa,sha256=i6Mfy4hc563X4f3qwPEsT8Bi9NaXBKLSz_nl2Tr9qMQ,2379
biorun/data/fasta_frame.fa,sha256=LZtoaLB4V4i6_iIbuICESAF7aXS-sDcuQRwE5B1iAbk,8
biorun/data/fasta_ids.fa,sha256=vSc0F495DeCkg9UsHF4oK7Sa6Ppl-SGw1jzzKMmHR3U,154
biorun/data/fasta_match.fa,sha256=ddjhsQ9ZtfaZdf24WRgn2Vbj-YWPlRBvmL2MH8EvVfI,327
biorun/data/fasta_multi.fa,sha256=Yxlev7ss0XCtqZFB8bKYqNIg-8y1ONrPpHWg5svjgpA,53
biorun/data/fasta_olap1.fa,sha256=TFh-AUxdKakwx3D9itQZAl22rCIJmXT5LaCyC4ldACA,221
biorun/data/fasta_protein.fa,sha256=qrJ4reaQR11lSlK8fNDljCYlehPKtERmoJA8eQelYc8,26397
biorun/data/fasta_rename1.fa,sha256=IsJ3MtSy18sAsEEiwuMDp0EUku1rMlF5l1Th9dv9mfo,410
biorun/data/fasta_s.fa,sha256=K2LEp1sY5a5_By4Vz5z_7Tm-ADuBKomE7WL_jUIzn_g,2776
biorun/data/fasta_start.fa,sha256=Pq_6ARex7Qrq_H1jgdl85zIeVYzE83urfiWBmsDaePQ,2313
biorun/data/fasta_stop.fa,sha256=KXpf15x0GM0fwDDP33bgZjpV3UqCOIIUyDnwUYGyltE,2225
biorun/data/fasta_translate.fa,sha256=Y_KCXJpp6JLknI8-UICx_C0-aH5BmRsTrJdawUloQVM,26419
biorun/data/fetch_enst.txt,sha256=fr0Nz80a0GJcNGqhfQRqrJV41yABpDCm63b7CWJIAew,193482
biorun/data/fetch_gff.gff,sha256=aigzeTiVAOdtstElucd9mNkdR42UVWgxT32bYZwlOKY,14719
biorun/data/fetch_prot.fa,sha256=pzHnw-Mr6DVWA_RoRo6SG59n2CioGYiLXz5GELESVm0,9137
biorun/data/file1.txt,sha256=S3lkvRw9LbmaJqNGxrfdZorEPtJj5K04DQMOODfi18o,12
biorun/data/file2.txt,sha256=DZCAfG3_jXb-BIxhThLpuMQvzGGJ98ChrPlaijaCVsE,12
biorun/data/format_mafft1.diff.txt,sha256=jJzPhhMm2sUGTUefP9HrGbjRiW6q-Qps-NcAZFL_Hdg,308
biorun/data/format_mafft1.txt,sha256=eGQ7ZC5R7w35lNETcRpj1xxcS3oOQGKg-aBs0_lgex8,677
biorun/data/format_mafft1.vcf,sha256=vIbI817_7vOVJlzXDhCNAVXpuNhXSsyvlu3xtPpURm0,760
biorun/data/genomes.fa,sha256=qzka6LlKrgNbsTdiF63bg828psfwjltwjt4D5-P9usQ,369
biorun/data/genomes.gb,sha256=3zPzV9Ic2l4biLgAQdFGeaJxs8B79i6Y88prX_M3V-M,135161
biorun/data/genomes.json,sha256=2-48d2iLKHYlBNoREHrnmgULQrHj9Mf-stJddJnCHAo,161771
biorun/data/genomes.jsonl,sha256=PfSy4LSn3N0-Z2v0F_0DmcXKYj_uZbxVvw9sIiZGEcM,111057
biorun/data/gff_CDS.gff,sha256=zGqGVB72iFYE4Xj2tVENj9Z20yguX0SeSNi5IowsDTk,1918
biorun/data/gff_all.gff,sha256=9D7ASlVFHernYKNV50OuZOi54KmCFGlXAAj0GibBW24,7872
biorun/data/gff_olap1.gff,sha256=kCzkuMucz3jMJeeJ2YZkrRCIv2aBd8Zt-3bneL74nBA,316
biorun/data/gff_slice.gff,sha256=O21dgpcAkGq3P8gmffYAcoLxcMDOt2hzCHu77Z-PM_M,542
biorun/data/ids.txt,sha256=-rmqJG3JK1kFZ2HceIBTNbJCYGL0jNhf6vtLJyfXask,97
biorun/data/lineage.txt,sha256=d5wv6NPAFPvBShT7tLF0gVz9XM28sCZbCTUGkVW9oqQ,1191
biorun/data/mafft.fa,sha256=x4-ab158jTBE-OAxFbT1AMVY_cXw-PgCsp_KqMJ_M00,410
biorun/data/meta.txt,sha256=jIQuMUELc1GJ5-oGtMZPJNpykbQtNgj8eivieMcfPAk,2044
biorun/data/nodiff.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
biorun/data/search_assembly.json,sha256=Uieh0loZo0HMG4xkYxF_nZpVYOgTrlIdoDNoWAUu-9Y,902
biorun/data/search_mygene.json,sha256=AhXEzb7rQQOZ212hVEw9LqzwPlhnRxVbcg8XTcI_u90,438
biorun/data/search_prjn.json,sha256=QXSnF9ltC_rLs7_RQCkju0FJjVfZ-sjy1joV3h2FR-U,6873
biorun/data/search_prjn.txt,sha256=isXquqFKiJQyCUtzWRvLua2-W7aKrHQMK9H-NTwYQVw,975
biorun/data/search_srr.json,sha256=Hua6uWiqe6xv8u4YxirudSqdbDKu6BMM692RKWUuujA,1097
biorun/data/table_1.txt,sha256=Y7YlocKH_Kl-poJ7gxDrGNoL_ijoHd5krkLkJDJC9ys,80
biorun/data/table_2.txt,sha256=HiCmxsCOISPPKO1YiKqOELFzZib3WkogI1deejZ8T04,2333
biorun/data/taxids.txt,sha256=bgUjJiBtD7ci2TcYAeFYt-VsuCVg_CQ08vFV_ItLMNg,13
biorun/data/taxon1.txt,sha256=D5p5iXvCMzmMbyJ1sbH-5C4wOk8f5ycCRt020jBdIJo,581
biorun/data/uniq0.txt,sha256=p_XPGf23eScrEr7RcTTGDRjUZP3f2lmbinD9xcqxhdE,8
biorun/data/uniq1.txt,sha256=HSB--quXoZ_Dl8ngLfwYyC6mcaEGL3OrwE1vVRz4CfU,10
biorun/data/uniq3.txt,sha256=aJ57hQIXfM0czYz5ySFrGZfWpymq6Ebn6qSFaLCs6-4,20
biorun/data/usage.sh,sha256=RIpWIsMz656tKEaGuBd7HuwGeC8jV8_mqz1lekn0Q-c,4880
biorun/data/weird.fa,sha256=8kb-svfQrhzyS68S5lPYuwWXGDlXjR7qWpc3oc_ybGY,56
biorun/enrichr.py,sha256=eEJgOqsEJIVseClo4iVpKhg6X0zSPtlXQBPeTU_Idfw,3271
biorun/fasta.py,sha256=71GGl6XctSvvJm9ut0Ef-wb-5rtina7P9hdKKCNQ6e8,1472
biorun/fetch.py,sha256=99b-aObo41VGaaTsKNdJOsFNhC6oK0FVeN_l9cLtWxE,10244
biorun/format.py,sha256=XRC1XaN2QQUmjJoJBzi1QKfNiDsSCSwKrBW4jrFAW44,1913
biorun/gff.py,sha256=ALJWt4VICdrA40UcbjNQfdjLagOgqQtA0bQVO8jQBg8,727
biorun/gprof.py,sha256=BUIBaM51E5JBHj817hmHhIZW7Tl3_Al-Ltsq3P4-Nn0,1840
biorun/gtf.py,sha256=f4gXx45hnvp39fhEZg-V5Dua6QQPnohP0iIEcg1m9us,2632
biorun/jsonrec.py,sha256=8hT0gLm8ICfPj6r6lBCI5vjOVFOe9u97Xd2EobWwUl4,1335
biorun/libs/README.md,sha256=IIvuW6O8omvGgFavHtF0s5M2wP5TS_-LljhkzzPl-lA,21
biorun/libs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
biorun/libs/__pycache__/__init__.cpython-310.pyc,,
biorun/libs/__pycache__/placlib.cpython-310.pyc,,
biorun/libs/__pycache__/sqlitedict.cpython-310.pyc,,
biorun/libs/__pycache__/xmltodict.cpython-310.pyc,,
biorun/libs/placlib.py,sha256=9kr6xFUXgtAVke9Q-0wnUKDGCuAyZLwEk9yW5Tlm9HM,15874
biorun/libs/sqlitedict.py,sha256=Se2cIJ8dUzinb2rOxEJKOtnKUZoEPgKPyC53gP6wC3E,20944
biorun/libs/xmltodict.py,sha256=hw-KPQFVEkfMMkgvVhuywi0Szo3NKpatL1UQxLBPPe0,19058
biorun/main.py,sha256=pKkvx9rjQ7TnnTz2ojaaVXWzQl7nCYCI-A1kmiBVeTQ,5552
biorun/mart.py,sha256=Z5ZsTM_tbyAh8zpIE7H9Sd-jTK0pDNH4GgyenTBNe_k,1669
biorun/meta.py,sha256=r-8yYHPXxepvfRmfEZOXNZGX6l7GlL4_sE6ogWPpFK8,1890
biorun/models.py,sha256=FKSswoVe41Pdfst2ALsrwkK9BLUxLVr7CJFSKoZQa38,9986
biorun/mygene.py,sha256=bW-6Hxg4t5Q7jIP8_qTSxaHbQ4MLAvy_uUz9iqwP9yc,1741
biorun/ontology.py,sha256=XhZ_hjgYstbUBrX2gFWTHFcSpXDnH5ucb-ynAvHfP7s,13618
biorun/parser.py,sha256=eit-OPhxw6mIjTarKB7YgKRjrx22uThEUB1SBGnXAVc,11489
biorun/patterns.py,sha256=VccBIsnT9ZzyFC5Nacnm3TCGs4yKpah0x-EKhZ-cNKM,243
biorun/scripts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
biorun/scripts/__pycache__/__init__.cpython-310.pyc,,
biorun/scripts/__pycache__/fasta_filter.cpython-310.pyc,,
biorun/scripts/fasta_filter.py,sha256=caMs2Lw9OW31oyXG-DN8qwr13zCF3mgf5hUbwpkjpmU,1554
biorun/search.py,sha256=isS_hckdw78ZI1Lo1JAl65diulzOB4as0WWiwbRLOuo,11124
biorun/search2.py,sha256=eNCeJhKs_KDtx9dF_Zd5EV-KjrqJsWiHPdMXFYsckRE,2079
biorun/table.py,sha256=3MWxPkvMmCGCB_MGN1UCJvDJi03MTYgFby2Lrbj0Q1U,992
biorun/taxon.py,sha256=UxEpRNUzUK8sVm7UMmnHyziKLi4Xvr4UjPzgRVa5AtA,15408
biorun/test.py,sha256=M_6qq7cggj3eOpOHvbO2iNzXSJmt2OybnnfvRa8pCiU,2752
biorun/tests/__init__.py,sha256=zWXrLXZKuj04k6kotaT9g9kHecHVwV5oCEYhu2ejvtI,112
biorun/tests/__pycache__/__init__.cpython-310.pyc,,
biorun/uniq.py,sha256=_AKgCviWLU8UCWRS_Rwip5gVzOK5oESXBjk_r7oKfyo,1626
biorun/utils.py,sha256=gnU40RYxOzgOojsHqk_6FOu9yOPYh1hFLwImf91ZYVU,12175
biorun/variant.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
biorun/vcf2fasta.py,sha256=UQYiXESpOWCydGMKNayEKOeiUu5mW1_CVzj17I2kRp4,3857
