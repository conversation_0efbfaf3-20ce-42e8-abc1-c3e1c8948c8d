{"version": 3, "file": "KHR_materials_diffuse_transmission-BcMbxGm7.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_diffuse_transmission.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_diffuse_transmission\";\n/**\n * [Proposed Specification](https://github.com/KhronosGroup/glTF/pull/1825)\n * !!! Experimental Extension Subject to Changes !!!\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_diffuse_transmission {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 174;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n        if (this.enabled) {\n            loader.parent.transparencyAsCoverage = true;\n        }\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadTranslucentPropertiesAsync(extensionContext, material, babylonMaterial, extension));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadTranslucentPropertiesAsync(context, material, babylonMaterial, extension) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const pbrMaterial = babylonMaterial;\n        // Enables \"translucency\" texture which represents diffusely-transmitted light.\n        pbrMaterial.subSurface.isTranslucencyEnabled = true;\n        // Since this extension models thin-surface transmission only, we must make the\n        // internal IOR == 1.0 and set the thickness to 0.\n        pbrMaterial.subSurface.volumeIndexOfRefraction = 1.0;\n        pbrMaterial.subSurface.minimumThickness = 0.0;\n        pbrMaterial.subSurface.maximumThickness = 0.0;\n        // Tint color will be used for transmission.\n        pbrMaterial.subSurface.useAlbedoToTintTranslucency = false;\n        if (extension.diffuseTransmissionFactor !== undefined) {\n            pbrMaterial.subSurface.translucencyIntensity = extension.diffuseTransmissionFactor;\n        }\n        else {\n            pbrMaterial.subSurface.translucencyIntensity = 0.0;\n            pbrMaterial.subSurface.isTranslucencyEnabled = false;\n            return Promise.resolve();\n        }\n        const promises = new Array();\n        pbrMaterial.subSurface.useGltfStyleTextures = true;\n        if (extension.diffuseTransmissionTexture) {\n            extension.diffuseTransmissionTexture.nonColorData = true;\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/diffuseTransmissionTexture`, extension.diffuseTransmissionTexture).then((texture) => {\n                texture.name = `${babylonMaterial.name} (Diffuse Transmission)`;\n                pbrMaterial.subSurface.translucencyIntensityTexture = texture;\n            }));\n        }\n        if (extension.diffuseTransmissionColorFactor !== undefined) {\n            pbrMaterial.subSurface.translucencyColor = Color3.FromArray(extension.diffuseTransmissionColorFactor);\n        }\n        else {\n            pbrMaterial.subSurface.translucencyColor = Color3.White();\n        }\n        if (extension.diffuseTransmissionColorTexture) {\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/diffuseTransmissionColorTexture`, extension.diffuseTransmissionColorTexture).then((texture) => {\n                texture.name = `${babylonMaterial.name} (Diffuse Transmission Color)`;\n                pbrMaterial.subSurface.translucencyColorTexture = texture;\n            }));\n        }\n        return Promise.all(promises).then(() => { });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_diffuse_transmission(loader));\n//# sourceMappingURL=KHR_materials_diffuse_transmission.js.map"], "names": ["NAME", "KHR_materials_diffuse_transmission", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "PBRMaterial", "pbrMaterial", "texture", "Color3", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "uTAIA,MAAMA,EAAO,qCAMN,MAAMC,CAAmC,CAI5C,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,EAC5C,KAAK,UACLE,EAAO,OAAO,uBAAyB,GAE9C,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BC,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,gCAAgCF,EAAkBH,EAAUC,EAAiBG,CAAS,CAAC,EACnG,QAAQ,IAAIC,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,gCAAgCN,EAASC,EAAUC,EAAiBG,EAAW,CAC3E,GAAI,EAAEH,aAA2BK,GAC7B,MAAM,IAAI,MAAM,GAAGP,CAAO,+BAA+B,EAE7D,MAAMQ,EAAcN,EAUpB,GARAM,EAAY,WAAW,sBAAwB,GAG/CA,EAAY,WAAW,wBAA0B,EACjDA,EAAY,WAAW,iBAAmB,EAC1CA,EAAY,WAAW,iBAAmB,EAE1CA,EAAY,WAAW,4BAA8B,GACjDH,EAAU,4BAA8B,OACxCG,EAAY,WAAW,sBAAwBH,EAAU,8BAGzD,QAAAG,EAAY,WAAW,sBAAwB,EAC/CA,EAAY,WAAW,sBAAwB,GACxC,QAAQ,UAEnB,MAAMF,EAAW,IAAI,MACrB,OAAAE,EAAY,WAAW,qBAAuB,GAC1CH,EAAU,6BACVA,EAAU,2BAA2B,aAAe,GACpDC,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,8BAA+BK,EAAU,0BAA0B,EAAE,KAAMI,GAAY,CAC7IA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,0BACtCM,EAAY,WAAW,6BAA+BC,CACzD,CAAA,CAAC,GAEFJ,EAAU,iCAAmC,OAC7CG,EAAY,WAAW,kBAAoBE,EAAO,UAAUL,EAAU,8BAA8B,EAGpGG,EAAY,WAAW,kBAAoBE,EAAO,MAAK,EAEvDL,EAAU,iCACVC,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,mCAAoCK,EAAU,+BAA+B,EAAE,KAAMI,GAAY,CACvJA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,gCACtCM,EAAY,WAAW,yBAA2BC,CACrD,CAAA,CAAC,EAEC,QAAQ,IAAIH,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CAC9C,CACL,CACAK,EAAwBd,CAAI,EAC5Be,EAAsBf,EAAM,GAAOE,GAAW,IAAID,EAAmCC,CAAM,CAAC", "x_google_ignoreList": [0]}