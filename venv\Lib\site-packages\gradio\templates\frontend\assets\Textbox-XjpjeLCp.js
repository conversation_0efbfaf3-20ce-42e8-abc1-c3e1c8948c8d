/* empty css                                                        */import{B as Ge}from"./BlockTitle-EkHcTHPF.js";import"./MarkdownCode.svelte_svelte_type_style_lang-DwuBo7pZ.js";import{C as Ie}from"./Check-CEkiXcyC.js";import{C as Je}from"./Copy-CxQ9EyK2.js";import{S as Oe}from"./Send-DyoOovnk.js";import{S as Pe}from"./Square-oAGqOwsh.js";import"./index-Co_Q4qaw.js";import{f as Qe}from"./index-DTl--HH3.js";/* empty css                                              */const{SvelteComponent:Re,action_destroyer:Ve,add_render_callback:We,append:j,attr:f,binding_callbacks:R,bubble:F,check_outros:U,create_component:A,create_in_transition:Xe,destroy_component:G,detach:H,element:q,empty:he,flush:y,group_outros:Y,init:Ze,insert:S,is_function:xe,listen:b,mount_component:I,noop:K,run_all:W,safe_not_equal:$e,set_data:le,set_input_value:L,space:V,text:ne,toggle_class:B,transition_in:k,transition_out:T}=window.__gradio__svelte__internal,{beforeUpdate:et,afterUpdate:tt,createEventDispatcher:lt,tick:fe}=window.__gradio__svelte__internal;function ae(l){let e,t,n,o;const s=[it,nt],u=[];function a(r,c){return r[18]?0:1}return e=a(l),t=u[e]=s[e](l),{c(){t.c(),n=he()},m(r,c){u[e].m(r,c),S(r,n,c),o=!0},p(r,c){let _=e;e=a(r),e===_?u[e].p(r,c):(Y(),T(u[_],1,1,()=>{u[_]=null}),U(),t=u[e],t?t.p(r,c):(t=u[e]=s[e](r),t.c()),k(t,1),t.m(n.parentNode,n))},i(r){o||(k(t),o=!0)},o(r){T(t),o=!1},d(r){r&&H(n),u[e].d(r)}}}function nt(l){let e,t,n,o,s;return t=new Je({}),{c(){e=q("button"),A(t.$$.fragment),f(e,"class","copy-button svelte-173056l"),f(e,"aria-label","Copy"),f(e,"aria-roledescription","Copy text")},m(u,a){S(u,e,a),I(t,e,null),n=!0,o||(s=b(e,"click",l[20]),o=!0)},p:K,i(u){n||(k(t.$$.fragment,u),n=!0)},o(u){T(t.$$.fragment,u),n=!1},d(u){u&&H(e),G(t),o=!1,s()}}}function it(l){let e,t,n,o;return t=new Ie({}),{c(){e=q("button"),A(t.$$.fragment),f(e,"class","copy-button svelte-173056l"),f(e,"aria-label","Copied"),f(e,"aria-roledescription","Text copied")},m(s,u){S(s,e,u),I(t,e,null),o=!0},p:K,i(s){o||(k(t.$$.fragment,s),s&&(n||We(()=>{n=Xe(e,Qe,{duration:300}),n.start()})),o=!0)},o(s){T(t.$$.fragment,s),o=!1},d(s){s&&H(e),G(t)}}}function ot(l){let e;return{c(){e=ne(l[3])},m(t,n){S(t,e,n)},p(t,n){n[0]&8&&le(e,t[3])},d(t){t&&H(e)}}}function st(l){let e,t,n,o,s,u;return{c(){e=q("textarea"),f(e,"data-testid","textbox"),f(e,"class","scroll-hide svelte-173056l"),f(e,"dir",t=l[12]?"rtl":"ltr"),f(e,"placeholder",l[2]),f(e,"rows",l[1]),e.disabled=l[5],e.autofocus=l[13],f(e,"maxlength",l[15]),f(e,"style",n=l[14]?"text-align: "+l[14]:""),B(e,"no-label",!l[6]&&(l[10]||l[11]))},m(a,r){S(a,e,r),L(e,l[0]),l[45](e),l[13]&&e.focus(),s||(u=[Ve(o=l[26].call(null,e,l[0])),b(e,"input",l[44]),b(e,"keypress",l[22]),b(e,"blur",l[36]),b(e,"select",l[21]),b(e,"focus",l[37]),b(e,"scroll",l[23])],s=!0)},p(a,r){r[0]&4096&&t!==(t=a[12]?"rtl":"ltr")&&f(e,"dir",t),r[0]&4&&f(e,"placeholder",a[2]),r[0]&2&&f(e,"rows",a[1]),r[0]&32&&(e.disabled=a[5]),r[0]&8192&&(e.autofocus=a[13]),r[0]&32768&&f(e,"maxlength",a[15]),r[0]&16384&&n!==(n=a[14]?"text-align: "+a[14]:"")&&f(e,"style",n),o&&xe(o.update)&&r[0]&1&&o.update.call(null,a[0]),r[0]&1&&L(e,a[0]),r[0]&3136&&B(e,"no-label",!a[6]&&(a[10]||a[11]))},d(a){a&&H(e),l[45](null),s=!1,W(u)}}}function ut(l){let e;function t(s,u){if(s[8]==="text")return at;if(s[8]==="password")return ft;if(s[8]==="email")return rt}let n=t(l),o=n&&n(l);return{c(){o&&o.c(),e=he()},m(s,u){o&&o.m(s,u),S(s,e,u)},p(s,u){n===(n=t(s))&&o?o.p(s,u):(o&&o.d(1),o=n&&n(s),o&&(o.c(),o.m(e.parentNode,e)))},d(s){s&&H(e),o&&o.d(s)}}}function rt(l){let e,t,n;return{c(){e=q("input"),f(e,"data-testid","textbox"),f(e,"type","email"),f(e,"class","scroll-hide svelte-173056l"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[13],f(e,"maxlength",l[15]),f(e,"autocomplete","email")},m(o,s){S(o,e,s),L(e,l[0]),l[43](e),l[13]&&e.focus(),t||(n=[b(e,"input",l[42]),b(e,"keypress",l[22]),b(e,"blur",l[34]),b(e,"select",l[21]),b(e,"focus",l[35])],t=!0)},p(o,s){s[0]&4&&f(e,"placeholder",o[2]),s[0]&32&&(e.disabled=o[5]),s[0]&8192&&(e.autofocus=o[13]),s[0]&32768&&f(e,"maxlength",o[15]),s[0]&1&&e.value!==o[0]&&L(e,o[0])},d(o){o&&H(e),l[43](null),t=!1,W(n)}}}function ft(l){let e,t,n;return{c(){e=q("input"),f(e,"data-testid","password"),f(e,"type","password"),f(e,"class","scroll-hide svelte-173056l"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[13],f(e,"maxlength",l[15]),f(e,"autocomplete","")},m(o,s){S(o,e,s),L(e,l[0]),l[41](e),l[13]&&e.focus(),t||(n=[b(e,"input",l[40]),b(e,"keypress",l[22]),b(e,"blur",l[32]),b(e,"select",l[21]),b(e,"focus",l[33])],t=!0)},p(o,s){s[0]&4&&f(e,"placeholder",o[2]),s[0]&32&&(e.disabled=o[5]),s[0]&8192&&(e.autofocus=o[13]),s[0]&32768&&f(e,"maxlength",o[15]),s[0]&1&&e.value!==o[0]&&L(e,o[0])},d(o){o&&H(e),l[41](null),t=!1,W(n)}}}function at(l){let e,t,n,o,s;return{c(){e=q("input"),f(e,"data-testid","textbox"),f(e,"type","text"),f(e,"class","scroll-hide svelte-173056l"),f(e,"dir",t=l[12]?"rtl":"ltr"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[13],f(e,"maxlength",l[15]),f(e,"style",n=l[14]?"text-align: "+l[14]:"")},m(u,a){S(u,e,a),L(e,l[0]),l[39](e),l[13]&&e.focus(),o||(s=[b(e,"input",l[38]),b(e,"keypress",l[22]),b(e,"blur",l[30]),b(e,"select",l[21]),b(e,"focus",l[31])],o=!0)},p(u,a){a[0]&4096&&t!==(t=u[12]?"rtl":"ltr")&&f(e,"dir",t),a[0]&4&&f(e,"placeholder",u[2]),a[0]&32&&(e.disabled=u[5]),a[0]&8192&&(e.autofocus=u[13]),a[0]&32768&&f(e,"maxlength",u[15]),a[0]&16384&&n!==(n=u[14]?"text-align: "+u[14]:"")&&f(e,"style",n),a[0]&1&&e.value!==u[0]&&L(e,u[0])},d(u){u&&H(e),l[39](null),o=!1,W(s)}}}function _e(l){let e,t,n,o,s,u;const a=[ct,_t],r=[];function c(_,p){return _[10]===!0?0:1}return t=c(l),n=r[t]=a[t](l),{c(){e=q("button"),n.c(),f(e,"class","submit-button svelte-173056l"),B(e,"padded-button",l[10]!==!0)},m(_,p){S(_,e,p),r[t].m(e,null),o=!0,s||(u=b(e,"click",l[25]),s=!0)},p(_,p){let d=t;t=c(_),t===d?r[t].p(_,p):(Y(),T(r[d],1,1,()=>{r[d]=null}),U(),n=r[t],n?n.p(_,p):(n=r[t]=a[t](_),n.c()),k(n,1),n.m(e,null)),(!o||p[0]&1024)&&B(e,"padded-button",_[10]!==!0)},i(_){o||(k(n),o=!0)},o(_){T(n),o=!1},d(_){_&&H(e),r[t].d(),s=!1,u()}}}function _t(l){let e;return{c(){e=ne(l[10])},m(t,n){S(t,e,n)},p(t,n){n[0]&1024&&le(e,t[10])},i:K,o:K,d(t){t&&H(e)}}}function ct(l){let e,t;return e=new Oe({}),{c(){A(e.$$.fragment)},m(n,o){I(e,n,o),t=!0},p:K,i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function ce(l){let e,t,n,o,s,u;const a=[bt,ht],r=[];function c(_,p){return _[11]===!0?0:1}return t=c(l),n=r[t]=a[t](l),{c(){e=q("button"),n.c(),f(e,"class","stop-button svelte-173056l"),B(e,"padded-button",l[11]!==!0)},m(_,p){S(_,e,p),r[t].m(e,null),o=!0,s||(u=b(e,"click",l[24]),s=!0)},p(_,p){let d=t;t=c(_),t===d?r[t].p(_,p):(Y(),T(r[d],1,1,()=>{r[d]=null}),U(),n=r[t],n?n.p(_,p):(n=r[t]=a[t](_),n.c()),k(n,1),n.m(e,null)),(!o||p[0]&2048)&&B(e,"padded-button",_[11]!==!0)},i(_){o||(k(n),o=!0)},o(_){T(n),o=!1},d(_){_&&H(e),r[t].d(),s=!1,u()}}}function ht(l){let e;return{c(){e=ne(l[11])},m(t,n){S(t,e,n)},p(t,n){n[0]&2048&&le(e,t[11])},i:K,o:K,d(t){t&&H(e)}}}function bt(l){let e,t;return e=new Pe({props:{fill:"none",stroke_width:2.5}}),{c(){A(e.$$.fragment)},m(n,o){I(e,n,o),t=!0},p:K,i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function dt(l){let e,t,n,o,s,u,a,r,c=l[6]&&l[9]&&ae(l);n=new Ge({props:{show_label:l[6],info:l[4],$$slots:{default:[ot]},$$scope:{ctx:l}}});function _(h,w){return h[1]===1&&h[17]===1?ut:st}let p=_(l),d=p(l),m=l[10]&&_e(l),g=l[11]&&ce(l);return{c(){e=q("label"),c&&c.c(),t=V(),A(n.$$.fragment),o=V(),s=q("div"),d.c(),u=V(),m&&m.c(),a=V(),g&&g.c(),f(s,"class","input-container svelte-173056l"),f(e,"class","svelte-173056l"),B(e,"container",l[7]),B(e,"show_textbox_border",l[19])},m(h,w){S(h,e,w),c&&c.m(e,null),j(e,t),I(n,e,null),j(e,o),j(e,s),d.m(s,null),j(s,u),m&&m.m(s,null),j(s,a),g&&g.m(s,null),r=!0},p(h,w){h[6]&&h[9]?c?(c.p(h,w),w[0]&576&&k(c,1)):(c=ae(h),c.c(),k(c,1),c.m(e,t)):c&&(Y(),T(c,1,1,()=>{c=null}),U());const M={};w[0]&64&&(M.show_label=h[6]),w[0]&16&&(M.info=h[4]),w[0]&8|w[1]&16777216&&(M.$$scope={dirty:w,ctx:h}),n.$set(M),p===(p=_(h))&&d?d.p(h,w):(d.d(1),d=p(h),d&&(d.c(),d.m(s,u))),h[10]?m?(m.p(h,w),w[0]&1024&&k(m,1)):(m=_e(h),m.c(),k(m,1),m.m(s,a)):m&&(Y(),T(m,1,1,()=>{m=null}),U()),h[11]?g?(g.p(h,w),w[0]&2048&&k(g,1)):(g=ce(h),g.c(),k(g,1),g.m(s,null)):g&&(Y(),T(g,1,1,()=>{g=null}),U()),(!r||w[0]&128)&&B(e,"container",h[7])},i(h){r||(k(c),k(n.$$.fragment,h),k(m),k(g),r=!0)},o(h){T(c),T(n.$$.fragment,h),T(m),T(g),r=!1},d(h){h&&H(e),c&&c.d(),G(n),d.d(),m&&m.d(),g&&g.d()}}}function mt(l,e,t){let{value:n=""}=e,{value_is_output:o=!1}=e,{lines:s=1}=e,{placeholder:u="Type here..."}=e,{label:a}=e,{info:r=void 0}=e,{disabled:c=!1}=e,{show_label:_=!0}=e,{container:p=!0}=e,{max_lines:d=void 0}=e,{type:m="text"}=e,{show_copy_button:g=!1}=e,{submit_btn:h=null}=e,{stop_btn:w=null}=e,{rtl:M=!1}=e,{autofocus:X=!1}=e,{text_align:ie=void 0}=e,{autoscroll:J=!0}=e,{max_length:oe=void 0}=e,v,Z=!1,x,$,se=0,O=!1,E;const be=!h,D=lt();et(()=>{!O&&v&&v.offsetHeight+v.scrollTop>v.scrollHeight-100&&($=!0)});const de=()=>{$&&J&&!O&&v.scrollTo(0,v.scrollHeight)};function me(){D("change",n),o||D("input")}tt(()=>{X&&v.focus(),$&&J&&de(),t(27,o=!1)});async function pe(){"clipboard"in navigator&&(await navigator.clipboard.writeText(n),D("copy",{value:n}),ge())}function ge(){t(18,Z=!0),x&&clearTimeout(x),x=setTimeout(()=>{t(18,Z=!1)},1e3)}function ke(i){const C=i.target,z=C.value,N=[C.selectionStart,C.selectionEnd];D("select",{value:z.substring(...N),index:N})}async function we(i){await fe(),(i.key==="Enter"&&i.shiftKey&&s>1||i.key==="Enter"&&!i.shiftKey&&s===1&&E>=1)&&(i.preventDefault(),D("submit"))}function ye(i){const C=i.target,z=C.scrollTop;z<se&&(O=!0),se=z;const N=C.scrollHeight-C.clientHeight;z>=N&&(O=!1)}function ve(){D("stop")}function Te(){D("submit")}async function P(i){if(await fe(),s===E)return;const C=i.target,z=window.getComputedStyle(C),N=parseFloat(z.paddingTop),ee=parseFloat(z.paddingBottom),ue=parseFloat(z.lineHeight);let te=E===void 0?!1:N+ee+ue*E,re=N+ee+s*ue;C.style.height="1px";let Q;te&&C.scrollHeight>te?Q=te:C.scrollHeight<re?Q=re:Q=C.scrollHeight,C.style.height=`${Q}px`}function Ce(i,C){if(s!==E&&(i.style.overflowY="scroll",i.addEventListener("input",P),!!C.trim()))return P({target:i}),{destroy:()=>i.removeEventListener("input",P)}}function He(i){F.call(this,l,i)}function Se(i){F.call(this,l,i)}function Ee(i){F.call(this,l,i)}function qe(i){F.call(this,l,i)}function ze(i){F.call(this,l,i)}function Be(i){F.call(this,l,i)}function De(i){F.call(this,l,i)}function Fe(i){F.call(this,l,i)}function Ke(){n=this.value,t(0,n)}function Le(i){R[i?"unshift":"push"](()=>{v=i,t(16,v)})}function Me(){n=this.value,t(0,n)}function Ne(i){R[i?"unshift":"push"](()=>{v=i,t(16,v)})}function Ue(){n=this.value,t(0,n)}function Ye(i){R[i?"unshift":"push"](()=>{v=i,t(16,v)})}function je(){n=this.value,t(0,n)}function Ae(i){R[i?"unshift":"push"](()=>{v=i,t(16,v)})}return l.$$set=i=>{"value"in i&&t(0,n=i.value),"value_is_output"in i&&t(27,o=i.value_is_output),"lines"in i&&t(1,s=i.lines),"placeholder"in i&&t(2,u=i.placeholder),"label"in i&&t(3,a=i.label),"info"in i&&t(4,r=i.info),"disabled"in i&&t(5,c=i.disabled),"show_label"in i&&t(6,_=i.show_label),"container"in i&&t(7,p=i.container),"max_lines"in i&&t(28,d=i.max_lines),"type"in i&&t(8,m=i.type),"show_copy_button"in i&&t(9,g=i.show_copy_button),"submit_btn"in i&&t(10,h=i.submit_btn),"stop_btn"in i&&t(11,w=i.stop_btn),"rtl"in i&&t(12,M=i.rtl),"autofocus"in i&&t(13,X=i.autofocus),"text_align"in i&&t(14,ie=i.text_align),"autoscroll"in i&&t(29,J=i.autoscroll),"max_length"in i&&t(15,oe=i.max_length)},l.$$.update=()=>{l.$$.dirty[0]&268435714&&(d===void 0?m==="text"?t(17,E=Math.max(s,20)):t(17,E=1):t(17,E=Math.max(d,s))),l.$$.dirty[0]&1&&n===null&&t(0,n=""),l.$$.dirty[0]&196611&&v&&s!==E&&P({target:v}),l.$$.dirty[0]&1&&me()},[n,s,u,a,r,c,_,p,m,g,h,w,M,X,ie,oe,v,E,Z,be,pe,ke,we,ye,ve,Te,Ce,o,d,J,He,Se,Ee,qe,ze,Be,De,Fe,Ke,Le,Me,Ne,Ue,Ye,je,Ae]}class Et extends Re{constructor(e){super(),Ze(this,e,mt,dt,$e,{value:0,value_is_output:27,lines:1,placeholder:2,label:3,info:4,disabled:5,show_label:6,container:7,max_lines:28,type:8,show_copy_button:9,submit_btn:10,stop_btn:11,rtl:12,autofocus:13,text_align:14,autoscroll:29,max_length:15},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),y()}get value_is_output(){return this.$$.ctx[27]}set value_is_output(e){this.$$set({value_is_output:e}),y()}get lines(){return this.$$.ctx[1]}set lines(e){this.$$set({lines:e}),y()}get placeholder(){return this.$$.ctx[2]}set placeholder(e){this.$$set({placeholder:e}),y()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),y()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),y()}get disabled(){return this.$$.ctx[5]}set disabled(e){this.$$set({disabled:e}),y()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),y()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),y()}get max_lines(){return this.$$.ctx[28]}set max_lines(e){this.$$set({max_lines:e}),y()}get type(){return this.$$.ctx[8]}set type(e){this.$$set({type:e}),y()}get show_copy_button(){return this.$$.ctx[9]}set show_copy_button(e){this.$$set({show_copy_button:e}),y()}get submit_btn(){return this.$$.ctx[10]}set submit_btn(e){this.$$set({submit_btn:e}),y()}get stop_btn(){return this.$$.ctx[11]}set stop_btn(e){this.$$set({stop_btn:e}),y()}get rtl(){return this.$$.ctx[12]}set rtl(e){this.$$set({rtl:e}),y()}get autofocus(){return this.$$.ctx[13]}set autofocus(e){this.$$set({autofocus:e}),y()}get text_align(){return this.$$.ctx[14]}set text_align(e){this.$$set({text_align:e}),y()}get autoscroll(){return this.$$.ctx[29]}set autoscroll(e){this.$$set({autoscroll:e}),y()}get max_length(){return this.$$.ctx[15]}set max_length(e){this.$$set({max_length:e}),y()}}export{Et as T};
//# sourceMappingURL=Textbox-XjpjeLCp.js.map
