import{b5 as k,R as D,A as n,O as x,V as m,ak as E,h as f,b as T,b7 as S,p as y,b2 as _,M,b0 as v,an as w,ao as N}from"./index-SLPGw9aX.js";import{ArrayItem as A,GLTFLoader as b}from"./glTFLoader-CeZAwvqd.js";import"./audioEngine-Ck6jhiVm.js";import"./index-Co_Q4qaw.js";import"./svelte/svelte.js";import"./bone-Ceu782jt.js";import"./rawTexture-BO6WiE_K.js";import"./assetContainer-9eHOOeSf.js";import"./objectModelMapping-Duj8W7QQ.js";class C{constructor(e,t,i){this.frame=e,this.action=t,this.onlyOnce=i,this.isDone=!1}_clone(){return new C(this.frame,this.action,this.onlyOnce)}}class g{get loop(){return this._loop}set loop(e){e!==this._loop&&(this._loop=e,this.updateOptions({loop:e}))}get currentTime(){if(this._htmlAudioElement)return this._htmlAudioElement.currentTime;if(n.audioEngine?.audioContext&&(this.isPlaying||this.isPaused)){const e=this.isPaused?0:n.audioEngine.audioContext.currentTime-this._startTime;return this._currentTime+e}return 0}get spatialSound(){return this._spatialSound}set spatialSound(e){if(e==this._spatialSound)return;const t=this.isPlaying;this.pause(),e?(this._spatialSound=e,this._updateSpatialParameters()):this._disableSpatialSound(),t&&this.play()}constructor(e,t,i,s=null,o){if(this.autoplay=!1,this._loop=!1,this.useCustomAttenuation=!1,this.isPlaying=!1,this.isPaused=!1,this.refDistance=1,this.rolloffFactor=1,this.maxDistance=100,this.distanceModel="linear",this.metadata=null,this.onEndedObservable=new x,this._spatialSound=!1,this._panningModel="equalpower",this._playbackRate=1,this._streaming=!1,this._startTime=0,this._currentTime=0,this._position=m.Zero(),this._localDirection=new m(1,0,0),this._volume=1,this._isReadyToPlay=!1,this._isDirectional=!1,this._coneInnerAngle=360,this._coneOuterAngle=360,this._coneOuterGain=0,this._isOutputConnected=!1,this._urlType="Unknown",this.name=e,i=i||E.LastCreatedScene,!!i)if(this._scene=i,g._SceneComponentInitialization(i),this._readyToPlayCallback=s,this._customAttenuationFunction=(r,u,d,l,h)=>u<d?r*(1-u/d):0,o&&(this.autoplay=o.autoplay||!1,this._loop=o.loop||!1,o.volume!==void 0&&(this._volume=o.volume),this._spatialSound=o.spatialSound??!1,this.maxDistance=o.maxDistance??100,this.useCustomAttenuation=o.useCustomAttenuation??!1,this.rolloffFactor=o.rolloffFactor||1,this.refDistance=o.refDistance||1,this.distanceModel=o.distanceModel||"linear",this._playbackRate=o.playbackRate||1,this._streaming=o.streaming??!1,this._length=o.length,this._offset=o.offset),n.audioEngine?.canUseWebAudio&&n.audioEngine.audioContext){this._soundGain=n.audioEngine.audioContext.createGain(),this._soundGain.gain.value=this._volume,this._inputAudioNode=this._soundGain,this._outputAudioNode=this._soundGain,this._spatialSound&&this._createSpatialParameters(),this._scene.mainSoundTrack.addSound(this);let r=!0;if(t)try{typeof t=="string"?(this._urlType="String",this._url=t):t instanceof ArrayBuffer?this._urlType="ArrayBuffer":t instanceof HTMLMediaElement?this._urlType="MediaElement":t instanceof MediaStream?this._urlType="MediaStream":t instanceof AudioBuffer?this._urlType="AudioBuffer":Array.isArray(t)&&(this._urlType="Array");let u=[],d=!1;switch(this._urlType){case"MediaElement":this._streaming=!0,this._isReadyToPlay=!0,this._streamingSource=n.audioEngine.audioContext.createMediaElementSource(t),this.autoplay&&this.play(0,this._offset,this._length),this._readyToPlayCallback&&this._readyToPlayCallback();break;case"MediaStream":this._streaming=!0,this._isReadyToPlay=!0,this._streamingSource=n.audioEngine.audioContext.createMediaStreamSource(t),this.autoplay&&this.play(0,this._offset,this._length),this._readyToPlayCallback&&this._readyToPlayCallback();break;case"ArrayBuffer":t.byteLength>0&&(d=!0,this._soundLoaded(t));break;case"AudioBuffer":this._audioBufferLoaded(t);break;case"String":u.push(t);case"Array":u.length===0&&(u=t);for(let l=0;l<u.length;l++){const h=u[l];if(d=o&&o.skipCodecCheck||h.indexOf(".mp3",h.length-4)!==-1&&n.audioEngine.isMP3supported||h.indexOf(".ogg",h.length-4)!==-1&&n.audioEngine.isOGGsupported||h.indexOf(".wav",h.length-4)!==-1||h.indexOf(".m4a",h.length-4)!==-1||h.indexOf(".mp4",h.length-4)!==-1||h.indexOf("blob:")!==-1,d){this._streaming?(this._htmlAudioElement=new Audio(h),this._htmlAudioElement.controls=!1,this._htmlAudioElement.loop=this.loop,T.SetCorsBehavior(h,this._htmlAudioElement),this._htmlAudioElement.preload="auto",this._htmlAudioElement.addEventListener("canplaythrough",()=>{this._isReadyToPlay=!0,this.autoplay&&this.play(0,this._offset,this._length),this._readyToPlayCallback&&this._readyToPlayCallback()},{once:!0}),document.body.appendChild(this._htmlAudioElement),this._htmlAudioElement.load()):this._scene._loadFile(h,p=>{this._soundLoaded(p)},void 0,!0,!0,p=>{p&&f.Error("XHR "+p.status+" error on: "+h+"."),f.Error("Sound creation aborted."),this._scene.mainSoundTrack.removeSound(this)});break}}break;default:r=!1;break}r?d||(this._isReadyToPlay=!0,this._readyToPlayCallback&&setTimeout(()=>{this._readyToPlayCallback&&this._readyToPlayCallback()},1e3)):f.Error("Parameter must be a URL to the sound, an Array of URLs (.mp3 & .ogg) or an ArrayBuffer of the sound.")}catch{f.Error("Unexpected error. Sound creation aborted."),this._scene.mainSoundTrack.removeSound(this)}}else this._scene.mainSoundTrack.addSound(this),n.audioEngine&&!n.audioEngine.WarnedWebAudioUnsupported&&(f.Error("Web Audio is not supported by your browser."),n.audioEngine.WarnedWebAudioUnsupported=!0),this._readyToPlayCallback&&setTimeout(()=>{this._readyToPlayCallback&&this._readyToPlayCallback()},1e3)}dispose(){n.audioEngine?.canUseWebAudio&&(this.isPlaying&&this.stop(),this._isReadyToPlay=!1,this.soundTrackId===-1?this._scene.mainSoundTrack.removeSound(this):this._scene.soundTracks&&this._scene.soundTracks[this.soundTrackId].removeSound(this),this._soundGain&&(this._soundGain.disconnect(),this._soundGain=null),this._soundPanner&&(this._soundPanner.disconnect(),this._soundPanner=null),this._soundSource&&(this._soundSource.disconnect(),this._soundSource=null),this._audioBuffer=null,this._htmlAudioElement&&(this._htmlAudioElement.pause(),this._htmlAudioElement.src="",document.body.removeChild(this._htmlAudioElement),this._htmlAudioElement=null),this._streamingSource&&(this._streamingSource.disconnect(),this._streamingSource=null),this._connectedTransformNode&&this._registerFunc&&(this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc),this._connectedTransformNode=null),this._clearTimeoutsAndObservers())}isReady(){return this._isReadyToPlay}getClassName(){return"Sound"}_audioBufferLoaded(e){n.audioEngine?.audioContext&&(this._audioBuffer=e,this._isReadyToPlay=!0,this.autoplay&&this.play(0,this._offset,this._length),this._readyToPlayCallback&&this._readyToPlayCallback())}_soundLoaded(e){n.audioEngine?.audioContext&&n.audioEngine.audioContext.decodeAudioData(e,t=>{this._audioBufferLoaded(t)},t=>{f.Error("Error while decoding audio data for: "+this.name+" / Error: "+t)})}setAudioBuffer(e){n.audioEngine?.canUseWebAudio&&(this._audioBuffer=e,this._isReadyToPlay=!0)}updateOptions(e){e&&(this.loop=e.loop??this.loop,this.maxDistance=e.maxDistance??this.maxDistance,this.useCustomAttenuation=e.useCustomAttenuation??this.useCustomAttenuation,this.rolloffFactor=e.rolloffFactor??this.rolloffFactor,this.refDistance=e.refDistance??this.refDistance,this.distanceModel=e.distanceModel??this.distanceModel,this._playbackRate=e.playbackRate??this._playbackRate,this._length=e.length??void 0,this.spatialSound=e.spatialSound??this._spatialSound,this._setOffset(e.offset??void 0),this.setVolume(e.volume??this._volume),this._updateSpatialParameters(),this.isPlaying&&(this._streaming&&this._htmlAudioElement?(this._htmlAudioElement.playbackRate=this._playbackRate,this._htmlAudioElement.loop!==this.loop&&(this._htmlAudioElement.loop=this.loop)):this._soundSource&&(this._soundSource.playbackRate.value=this._playbackRate,this._soundSource.loop!==this.loop&&(this._soundSource.loop=this.loop),this._offset!==void 0&&this._soundSource.loopStart!==this._offset&&(this._soundSource.loopStart=this._offset),this._length!==void 0&&this._length!==this._soundSource.loopEnd&&(this._soundSource.loopEnd=(this._offset|0)+this._length))))}_createSpatialParameters(){n.audioEngine?.canUseWebAudio&&n.audioEngine.audioContext&&(this._scene.headphone&&(this._panningModel="HRTF"),this._soundPanner=this._soundPanner??n.audioEngine.audioContext.createPanner(),this._soundPanner&&this._outputAudioNode&&(this._updateSpatialParameters(),this._soundPanner.connect(this._outputAudioNode),this._inputAudioNode=this._soundPanner))}_disableSpatialSound(){this._spatialSound&&(this._inputAudioNode=this._soundGain,this._soundPanner?.disconnect(),this._soundPanner=null,this._spatialSound=!1)}_updateSpatialParameters(){this._spatialSound&&(this._soundPanner?this.useCustomAttenuation?(this._soundPanner.distanceModel="linear",this._soundPanner.maxDistance=Number.MAX_VALUE,this._soundPanner.refDistance=1,this._soundPanner.rolloffFactor=1,this._soundPanner.panningModel=this._panningModel):(this._soundPanner.distanceModel=this.distanceModel,this._soundPanner.maxDistance=this.maxDistance,this._soundPanner.refDistance=this.refDistance,this._soundPanner.rolloffFactor=this.rolloffFactor,this._soundPanner.panningModel=this._panningModel):this._createSpatialParameters())}switchPanningModelToHRTF(){this._panningModel="HRTF",this._switchPanningModel()}switchPanningModelToEqualPower(){this._panningModel="equalpower",this._switchPanningModel()}_switchPanningModel(){n.audioEngine?.canUseWebAudio&&this._spatialSound&&this._soundPanner&&(this._soundPanner.panningModel=this._panningModel)}connectToSoundTrackAudioNode(e){n.audioEngine?.canUseWebAudio&&this._outputAudioNode&&(this._isOutputConnected&&this._outputAudioNode.disconnect(),this._outputAudioNode.connect(e),this._isOutputConnected=!0)}setDirectionalCone(e,t,i){if(t<e){f.Error("setDirectionalCone(): outer angle of the cone must be superior or equal to the inner angle.");return}this._coneInnerAngle=e,this._coneOuterAngle=t,this._coneOuterGain=i,this._isDirectional=!0,this.isPlaying&&this.loop&&(this.stop(),this.play(0,this._offset,this._length))}get directionalConeInnerAngle(){return this._coneInnerAngle}set directionalConeInnerAngle(e){if(e!=this._coneInnerAngle){if(this._coneOuterAngle<e){f.Error("directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.");return}this._coneInnerAngle=e,n.audioEngine?.canUseWebAudio&&this._spatialSound&&this._soundPanner&&(this._soundPanner.coneInnerAngle=this._coneInnerAngle)}}get directionalConeOuterAngle(){return this._coneOuterAngle}set directionalConeOuterAngle(e){if(e!=this._coneOuterAngle){if(e<this._coneInnerAngle){f.Error("directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.");return}this._coneOuterAngle=e,n.audioEngine?.canUseWebAudio&&this._spatialSound&&this._soundPanner&&(this._soundPanner.coneOuterAngle=this._coneOuterAngle)}}setPosition(e){e.equals(this._position)||(this._position.copyFrom(e),n.audioEngine?.canUseWebAudio&&this._spatialSound&&this._soundPanner&&!isNaN(this._position.x)&&!isNaN(this._position.y)&&!isNaN(this._position.z)&&(this._soundPanner.positionX.value=this._position.x,this._soundPanner.positionY.value=this._position.y,this._soundPanner.positionZ.value=this._position.z))}setLocalDirectionToMesh(e){this._localDirection=e,n.audioEngine?.canUseWebAudio&&this._connectedTransformNode&&this.isPlaying&&this._updateDirection()}_updateDirection(){if(!this._connectedTransformNode||!this._soundPanner)return;const e=this._connectedTransformNode.getWorldMatrix(),t=m.TransformNormal(this._localDirection,e);t.normalize(),this._soundPanner.orientationX.value=t.x,this._soundPanner.orientationY.value=t.y,this._soundPanner.orientationZ.value=t.z}updateDistanceFromListener(){if(n.audioEngine?.canUseWebAudio&&this._connectedTransformNode&&this.useCustomAttenuation&&this._soundGain&&this._scene.activeCamera){const e=this._scene.audioListenerPositionProvider?this._connectedTransformNode.position.subtract(this._scene.audioListenerPositionProvider()).length():this._connectedTransformNode.getDistanceToCamera(this._scene.activeCamera);this._soundGain.gain.value=this._customAttenuationFunction(this._volume,e,this.maxDistance,this.refDistance,this.rolloffFactor)}}setAttenuationFunction(e){this._customAttenuationFunction=e}play(e,t,i){if(this._isReadyToPlay&&this._scene.audioEnabled&&n.audioEngine?.audioContext)try{this._clearTimeoutsAndObservers();let s=e?n.audioEngine?.audioContext.currentTime+e:n.audioEngine?.audioContext.currentTime;if((!this._soundSource||!this._streamingSource)&&this._spatialSound&&this._soundPanner&&(!isNaN(this._position.x)&&!isNaN(this._position.y)&&!isNaN(this._position.z)&&(this._soundPanner.positionX.value=this._position.x,this._soundPanner.positionY.value=this._position.y,this._soundPanner.positionZ.value=this._position.z),this._isDirectional&&(this._soundPanner.coneInnerAngle=this._coneInnerAngle,this._soundPanner.coneOuterAngle=this._coneOuterAngle,this._soundPanner.coneOuterGain=this._coneOuterGain,this._connectedTransformNode?this._updateDirection():this._soundPanner.setOrientation(this._localDirection.x,this._localDirection.y,this._localDirection.z))),this._streaming){if(!this._streamingSource&&this._htmlAudioElement&&(this._streamingSource=n.audioEngine.audioContext.createMediaElementSource(this._htmlAudioElement),this._htmlAudioElement.onended=()=>{this._onended()},this._htmlAudioElement.playbackRate=this._playbackRate),this._streamingSource&&(this._streamingSource.disconnect(),this._inputAudioNode&&this._streamingSource.connect(this._inputAudioNode)),this._htmlAudioElement){const o=()=>{if(n.audioEngine?.unlocked){if(!this._htmlAudioElement)return;this._htmlAudioElement.currentTime=t??0;const r=this._htmlAudioElement.play();r!==void 0&&r.catch(()=>{n.audioEngine?.lock(),(this.loop||this.autoplay)&&(this._audioUnlockedObserver=n.audioEngine?.onAudioUnlockedObservable.addOnce(()=>{o()}))})}else(this.loop||this.autoplay)&&(this._audioUnlockedObserver=n.audioEngine?.onAudioUnlockedObservable.addOnce(()=>{o()}))};o()}}else{const o=()=>{if(n.audioEngine?.audioContext){if(i=i||this._length,t!==void 0&&this._setOffset(t),this._soundSource){const r=this._soundSource;r.onended=()=>{r.disconnect()}}if(this._soundSource=n.audioEngine?.audioContext.createBufferSource(),this._soundSource&&this._inputAudioNode){this._soundSource.buffer=this._audioBuffer,this._soundSource.connect(this._inputAudioNode),this._soundSource.loop=this.loop,t!==void 0&&(this._soundSource.loopStart=t),i!==void 0&&(this._soundSource.loopEnd=(t|0)+i),this._soundSource.playbackRate.value=this._playbackRate,this._soundSource.onended=()=>{this._onended()},s=e?n.audioEngine?.audioContext.currentTime+e:n.audioEngine.audioContext.currentTime;const r=((this.isPaused?this.currentTime:0)+(this._offset??0))%this._soundSource.buffer.duration;this._soundSource.start(s,r,this.loop?void 0:i)}}};n.audioEngine?.audioContext.state==="suspended"?this._tryToPlayTimeout=setTimeout(()=>{n.audioEngine?.audioContext.state==="suspended"?(n.audioEngine.lock(),(this.loop||this.autoplay)&&(this._audioUnlockedObserver=n.audioEngine.onAudioUnlockedObservable.addOnce(()=>{o()}))):o()},500):o()}this._startTime=s,this.isPlaying=!0,this.isPaused=!1}catch(s){f.Error("Error while trying to play audio: "+this.name+", "+s.message)}}_onended(){this.isPlaying=!1,this._startTime=0,this._currentTime=0,this.onended&&this.onended(),this.onEndedObservable.notifyObservers(this)}stop(e){if(this.isPlaying)if(this._clearTimeoutsAndObservers(),this._streaming)this._htmlAudioElement?(this._htmlAudioElement.pause(),this._htmlAudioElement.currentTime>0&&(this._htmlAudioElement.currentTime=0)):this._streamingSource?.disconnect(),this.isPlaying=!1;else if(n.audioEngine?.audioContext&&this._soundSource){const t=e?n.audioEngine.audioContext.currentTime+e:void 0;this._soundSource.onended=()=>{this.isPlaying=!1,this.isPaused=!1,this._startTime=0,this._currentTime=0,this._soundSource&&(this._soundSource.onended=()=>{}),this._onended()},this._soundSource.stop(t)}else this.isPlaying=!1;else this.isPaused&&(this.isPaused=!1,this._startTime=0,this._currentTime=0)}pause(){this.isPlaying&&(this._clearTimeoutsAndObservers(),this._streaming?(this._htmlAudioElement?this._htmlAudioElement.pause():this._streamingSource?.disconnect(),this.isPlaying=!1,this.isPaused=!0):n.audioEngine?.audioContext&&this._soundSource&&(this._soundSource.onended=()=>{},this._soundSource.stop(),this.isPlaying=!1,this.isPaused=!0,this._currentTime+=n.audioEngine.audioContext.currentTime-this._startTime))}setVolume(e,t){n.audioEngine?.canUseWebAudio&&this._soundGain&&(t&&n.audioEngine.audioContext?(this._soundGain.gain.cancelScheduledValues(n.audioEngine.audioContext.currentTime),this._soundGain.gain.setValueAtTime(this._soundGain.gain.value,n.audioEngine.audioContext.currentTime),this._soundGain.gain.linearRampToValueAtTime(e,n.audioEngine.audioContext.currentTime+t)):this._soundGain.gain.value=e),this._volume=e}setPlaybackRate(e){this._playbackRate=e,this.isPlaying&&(this._streaming&&this._htmlAudioElement?this._htmlAudioElement.playbackRate=this._playbackRate:this._soundSource&&(this._soundSource.playbackRate.value=this._playbackRate))}getPlaybackRate(){return this._playbackRate}getVolume(){return this._volume}attachToMesh(e){this._connectedTransformNode&&this._registerFunc&&(this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc),this._registerFunc=null),this._connectedTransformNode=e,this._spatialSound||(this._spatialSound=!0,this._createSpatialParameters(),this.isPlaying&&this.loop&&(this.stop(),this.play(0,this._offset,this._length))),this._onRegisterAfterWorldMatrixUpdate(this._connectedTransformNode),this._registerFunc=t=>this._onRegisterAfterWorldMatrixUpdate(t),this._connectedTransformNode.registerAfterWorldMatrixUpdate(this._registerFunc)}detachFromMesh(){this._connectedTransformNode&&this._registerFunc&&(this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc),this._registerFunc=null,this._connectedTransformNode=null)}_onRegisterAfterWorldMatrixUpdate(e){if(!e.getBoundingInfo)this.setPosition(e.absolutePosition);else{const i=e.getBoundingInfo();this.setPosition(i.boundingSphere.centerWorld)}n.audioEngine?.canUseWebAudio&&this._isDirectional&&this.isPlaying&&this._updateDirection()}clone(){if(this._streaming)return null;{const e=()=>{S(()=>this._isReadyToPlay,()=>{i._audioBuffer=this.getAudioBuffer(),i._isReadyToPlay=!0,i.autoplay&&i.play(0,this._offset,this._length)},void 0,300)},t={autoplay:this.autoplay,loop:this.loop,volume:this._volume,spatialSound:this._spatialSound,maxDistance:this.maxDistance,useCustomAttenuation:this.useCustomAttenuation,rolloffFactor:this.rolloffFactor,refDistance:this.refDistance,distanceModel:this.distanceModel},i=new g(this.name+"_cloned",new ArrayBuffer(0),this._scene,null,t);return this.useCustomAttenuation&&i.setAttenuationFunction(this._customAttenuationFunction),i.setPosition(this._position),i.setPlaybackRate(this._playbackRate),e(),i}}getAudioBuffer(){return this._audioBuffer}getSoundSource(){return this._soundSource}getSoundGain(){return this._soundGain}serialize(){const e={name:this.name,url:this._url,autoplay:this.autoplay,loop:this.loop,volume:this._volume,spatialSound:this._spatialSound,maxDistance:this.maxDistance,rolloffFactor:this.rolloffFactor,refDistance:this.refDistance,distanceModel:this.distanceModel,playbackRate:this._playbackRate,panningModel:this._panningModel,soundTrackId:this.soundTrackId,metadata:this.metadata};return this._spatialSound&&(this._connectedTransformNode&&(e.connectedMeshId=this._connectedTransformNode.id),e.position=this._position.asArray(),e.refDistance=this.refDistance,e.distanceModel=this.distanceModel,e.isDirectional=this._isDirectional,e.localDirectionToMesh=this._localDirection.asArray(),e.coneInnerAngle=this._coneInnerAngle,e.coneOuterAngle=this._coneOuterAngle,e.coneOuterGain=this._coneOuterGain),e}static Parse(e,t,i,s){const o=e.name;let r;e.url?r=i+e.url:r=i+o;const u={autoplay:e.autoplay,loop:e.loop,volume:e.volume,spatialSound:e.spatialSound,maxDistance:e.maxDistance,rolloffFactor:e.rolloffFactor,refDistance:e.refDistance,distanceModel:e.distanceModel,playbackRate:e.playbackRate};let d;if(!s)d=new g(o,r,t,()=>{t.removePendingData(d)},u),t.addPendingData(d);else{const l=()=>{S(()=>s._isReadyToPlay,()=>{d._audioBuffer=s.getAudioBuffer(),d._isReadyToPlay=!0,d.autoplay&&d.play(0,d._offset,d._length)},void 0,300)};d=new g(o,new ArrayBuffer(0),t,null,u),l()}if(e.position){const l=m.FromArray(e.position);d.setPosition(l)}if(e.isDirectional&&(d.setDirectionalCone(e.coneInnerAngle||360,e.coneOuterAngle||360,e.coneOuterGain||0),e.localDirectionToMesh)){const l=m.FromArray(e.localDirectionToMesh);d.setLocalDirectionToMesh(l)}if(e.connectedMeshId){const l=t.getMeshById(e.connectedMeshId);l&&d.attachToMesh(l)}return e.metadata&&(d.metadata=e.metadata),d}_setOffset(e){this._offset!==e&&(this.isPaused&&(this.stop(),this.isPaused=!1),this._offset=e)}_clearTimeoutsAndObservers(){this._tryToPlayTimeout&&(clearTimeout(this._tryToPlayTimeout),this._tryToPlayTimeout=null),this._audioUnlockedObserver&&(n.audioEngine?.onAudioUnlockedObservable.remove(this._audioUnlockedObserver),this._audioUnlockedObserver=null)}}g._SceneComponentInitialization=a=>{throw k("AudioSceneComponent")};D("BABYLON.Sound",g);class R{constructor(e,t,i){if(this.loop=!1,this._coneInnerAngle=360,this._coneOuterAngle=360,this._volume=1,this.isPlaying=!1,this.isPaused=!1,this._sounds=[],this._weights=[],t.length!==i.length)throw new Error("Sounds length does not equal weights length");this.loop=e,this._weights=i;let s=0;for(const r of i)s+=r;const o=s>0?1/s:0;for(let r=0;r<this._weights.length;r++)this._weights[r]*=o;this._sounds=t;for(const r of this._sounds)r.onEndedObservable.add(()=>{this._onended()})}get directionalConeInnerAngle(){return this._coneInnerAngle}set directionalConeInnerAngle(e){if(e!==this._coneInnerAngle){if(this._coneOuterAngle<e){f.Error("directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.");return}this._coneInnerAngle=e;for(const t of this._sounds)t.directionalConeInnerAngle=e}}get directionalConeOuterAngle(){return this._coneOuterAngle}set directionalConeOuterAngle(e){if(e!==this._coneOuterAngle){if(e<this._coneInnerAngle){f.Error("directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.");return}this._coneOuterAngle=e;for(const t of this._sounds)t.directionalConeOuterAngle=e}}get volume(){return this._volume}set volume(e){if(e!==this._volume)for(const t of this._sounds)t.setVolume(e)}_onended(){this._currentIndex!==void 0&&(this._sounds[this._currentIndex].autoplay=!1),this.loop&&this.isPlaying?this.play():this.isPlaying=!1}pause(){this.isPlaying&&(this.isPaused=!0,this._currentIndex!==void 0&&this._sounds[this._currentIndex].pause())}stop(){this.isPlaying=!1,this._currentIndex!==void 0&&this._sounds[this._currentIndex].stop()}play(e){if(!this.isPaused){this.stop();const i=Math.random();let s=0;for(let o=0;o<this._weights.length;o++)if(s+=this._weights[o],i<=s){this._currentIndex=o;break}}const t=this._sounds[this._currentIndex??0];t.isReady()?t.play(0,this.isPaused?void 0:e):t.autoplay=!0,this.isPlaying=!0,this.isPaused=!1}}class O{constructor(e,t={}){this.id=-1,this._isInitialized=!1,e=e||E.LastCreatedScene,e&&(this._scene=e,this.soundCollection=[],this._options=t,!this._options.mainTrack&&this._scene.soundTracks&&(this._scene.soundTracks.push(this),this.id=this._scene.soundTracks.length-1))}_initializeSoundTrackAudioGraph(){n.audioEngine?.canUseWebAudio&&n.audioEngine.audioContext&&(this._outputAudioNode=n.audioEngine.audioContext.createGain(),this._outputAudioNode.connect(n.audioEngine.masterGain),this._options&&this._options.volume&&(this._outputAudioNode.gain.value=this._options.volume),this._isInitialized=!0)}dispose(){if(n.audioEngine&&n.audioEngine.canUseWebAudio){for(this._connectedAnalyser&&this._connectedAnalyser.stopDebugCanvas();this.soundCollection.length;)this.soundCollection[0].dispose();this._outputAudioNode&&this._outputAudioNode.disconnect(),this._outputAudioNode=null}}addSound(e){this._isInitialized||this._initializeSoundTrackAudioGraph(),n.audioEngine?.canUseWebAudio&&this._outputAudioNode&&e.connectToSoundTrackAudioNode(this._outputAudioNode),e.soundTrackId!==void 0&&(e.soundTrackId===-1?this._scene.mainSoundTrack.removeSound(e):this._scene.soundTracks&&this._scene.soundTracks[e.soundTrackId].removeSound(e)),this.soundCollection.push(e),e.soundTrackId=this.id}removeSound(e){const t=this.soundCollection.indexOf(e);t!==-1&&this.soundCollection.splice(t,1)}setVolume(e){n.audioEngine?.canUseWebAudio&&this._outputAudioNode&&(this._outputAudioNode.gain.value=e)}switchPanningModelToHRTF(){if(n.audioEngine?.canUseWebAudio)for(let e=0;e<this.soundCollection.length;e++)this.soundCollection[e].switchPanningModelToHRTF()}switchPanningModelToEqualPower(){if(n.audioEngine?.canUseWebAudio)for(let e=0;e<this.soundCollection.length;e++)this.soundCollection[e].switchPanningModelToEqualPower()}connectToAnalyser(e){this._connectedAnalyser&&this._connectedAnalyser.stopDebugCanvas(),this._connectedAnalyser=e,n.audioEngine?.canUseWebAudio&&this._outputAudioNode&&(this._outputAudioNode.disconnect(),this._connectedAnalyser.connectAudioNodes(this._outputAudioNode,n.audioEngine.masterGain))}}Object.defineProperty(y.prototype,"mainSoundTrack",{get:function(){let a=this._getComponent(_.NAME_AUDIO);return a||(a=new c(this),this._addComponent(a)),this._mainSoundTrack||(this._mainSoundTrack=new O(this,{mainTrack:!0})),this._mainSoundTrack},enumerable:!0,configurable:!0});y.prototype.getSoundByName=function(a){let e;for(e=0;e<this.mainSoundTrack.soundCollection.length;e++)if(this.mainSoundTrack.soundCollection[e].name===a)return this.mainSoundTrack.soundCollection[e];if(this.soundTracks){for(let t=0;t<this.soundTracks.length;t++)for(e=0;e<this.soundTracks[t].soundCollection.length;e++)if(this.soundTracks[t].soundCollection[e].name===a)return this.soundTracks[t].soundCollection[e]}return null};Object.defineProperty(y.prototype,"audioEnabled",{get:function(){let a=this._getComponent(_.NAME_AUDIO);return a||(a=new c(this),this._addComponent(a)),a.audioEnabled},set:function(a){let e=this._getComponent(_.NAME_AUDIO);e||(e=new c(this),this._addComponent(e)),a?e.enableAudio():e.disableAudio()},enumerable:!0,configurable:!0});Object.defineProperty(y.prototype,"headphone",{get:function(){let a=this._getComponent(_.NAME_AUDIO);return a||(a=new c(this),this._addComponent(a)),a.headphone},set:function(a){let e=this._getComponent(_.NAME_AUDIO);e||(e=new c(this),this._addComponent(e)),a?e.switchAudioModeForHeadphones():e.switchAudioModeForNormalSpeakers()},enumerable:!0,configurable:!0});Object.defineProperty(y.prototype,"audioListenerPositionProvider",{get:function(){let a=this._getComponent(_.NAME_AUDIO);return a||(a=new c(this),this._addComponent(a)),a.audioListenerPositionProvider},set:function(a){let e=this._getComponent(_.NAME_AUDIO);if(e||(e=new c(this),this._addComponent(e)),a&&typeof a!="function")throw new Error("The value passed to [Scene.audioListenerPositionProvider] must be a function that returns a Vector3");e.audioListenerPositionProvider=a},enumerable:!0,configurable:!0});Object.defineProperty(y.prototype,"audioListenerRotationProvider",{get:function(){let a=this._getComponent(_.NAME_AUDIO);return a||(a=new c(this),this._addComponent(a)),a.audioListenerRotationProvider},set:function(a){let e=this._getComponent(_.NAME_AUDIO);if(e||(e=new c(this),this._addComponent(e)),a&&typeof a!="function")throw new Error("The value passed to [Scene.audioListenerRotationProvider] must be a function that returns a Vector3");e.audioListenerRotationProvider=a},enumerable:!0,configurable:!0});Object.defineProperty(y.prototype,"audioPositioningRefreshRate",{get:function(){let a=this._getComponent(_.NAME_AUDIO);return a||(a=new c(this),this._addComponent(a)),a.audioPositioningRefreshRate},set:function(a){let e=this._getComponent(_.NAME_AUDIO);e||(e=new c(this),this._addComponent(e)),e.audioPositioningRefreshRate=a},enumerable:!0,configurable:!0});class c{get audioEnabled(){return this._audioEnabled}get headphone(){return this._headphone}constructor(e){this.name=_.NAME_AUDIO,this._audioEnabled=!0,this._headphone=!1,this.audioPositioningRefreshRate=500,this.audioListenerPositionProvider=null,this.audioListenerRotationProvider=null,this._cachedCameraDirection=new m,this._cachedCameraPosition=new m,this._lastCheck=0,this._invertMatrixTemp=new M,this._cameraDirectionTemp=new m,e=e||E.LastCreatedScene,e&&(this.scene=e,e.soundTracks=[],e.sounds=[])}register(){this.scene._afterRenderStage.registerStep(_.STEP_AFTERRENDER_AUDIO,this,this._afterRender)}rebuild(){}serialize(e){if(e.sounds=[],this.scene.soundTracks)for(let t=0;t<this.scene.soundTracks.length;t++){const i=this.scene.soundTracks[t];for(let s=0;s<i.soundCollection.length;s++)e.sounds.push(i.soundCollection[s].serialize())}}addFromContainer(e){e.sounds&&e.sounds.forEach(t=>{t.play(),t.autoplay=!0,this.scene.mainSoundTrack.addSound(t)})}removeFromContainer(e,t=!1){e.sounds&&e.sounds.forEach(i=>{i.stop(),i.autoplay=!1,this.scene.mainSoundTrack.removeSound(i),t&&i.dispose()})}dispose(){const e=this.scene;if(e._mainSoundTrack&&e.mainSoundTrack.dispose(),e.soundTracks)for(let t=0;t<e.soundTracks.length;t++)e.soundTracks[t].dispose()}disableAudio(){const e=this.scene;this._audioEnabled=!1,n.audioEngine&&n.audioEngine.audioContext&&n.audioEngine.audioContext.suspend();let t;for(t=0;t<e.mainSoundTrack.soundCollection.length;t++)e.mainSoundTrack.soundCollection[t].pause();if(e.soundTracks)for(t=0;t<e.soundTracks.length;t++)for(let i=0;i<e.soundTracks[t].soundCollection.length;i++)e.soundTracks[t].soundCollection[i].pause()}enableAudio(){const e=this.scene;this._audioEnabled=!0,n.audioEngine&&n.audioEngine.audioContext&&n.audioEngine.audioContext.resume();let t;for(t=0;t<e.mainSoundTrack.soundCollection.length;t++)e.mainSoundTrack.soundCollection[t].isPaused&&e.mainSoundTrack.soundCollection[t].play();if(e.soundTracks)for(t=0;t<e.soundTracks.length;t++)for(let i=0;i<e.soundTracks[t].soundCollection.length;i++)e.soundTracks[t].soundCollection[i].isPaused&&e.soundTracks[t].soundCollection[i].play()}switchAudioModeForHeadphones(){const e=this.scene;if(this._headphone=!0,e.mainSoundTrack.switchPanningModelToHRTF(),e.soundTracks)for(let t=0;t<e.soundTracks.length;t++)e.soundTracks[t].switchPanningModelToHRTF()}switchAudioModeForNormalSpeakers(){const e=this.scene;if(this._headphone=!1,e.mainSoundTrack.switchPanningModelToEqualPower(),e.soundTracks)for(let t=0;t<e.soundTracks.length;t++)e.soundTracks[t].switchPanningModelToEqualPower()}_afterRender(){const e=v.Now;if(this._lastCheck&&e-this._lastCheck<this.audioPositioningRefreshRate)return;this._lastCheck=e;const t=this.scene;if(!this._audioEnabled||!t._mainSoundTrack||!t.soundTracks||t._mainSoundTrack.soundCollection.length===0&&t.soundTracks.length===1)return;const i=n.audioEngine;if(i&&i.audioContext){let s=t.activeCamera;if(t.activeCameras&&t.activeCameras.length>0&&(s=t.activeCameras[0]),this.audioListenerPositionProvider){const r=this.audioListenerPositionProvider();i.audioContext.listener.setPosition(r.x||0,r.y||0,r.z||0)}else s?this._cachedCameraPosition.equals(s.globalPosition)||(this._cachedCameraPosition.copyFrom(s.globalPosition),i.audioContext.listener.setPosition(s.globalPosition.x,s.globalPosition.y,s.globalPosition.z)):i.audioContext.listener.setPosition(0,0,0);if(this.audioListenerRotationProvider){const r=this.audioListenerRotationProvider();i.audioContext.listener.setOrientation(r.x||0,r.y||0,r.z||0,0,1,0)}else s?(s.rigCameras&&s.rigCameras.length>0&&(s=s.rigCameras[0]),s.getViewMatrix().invertToRef(this._invertMatrixTemp),m.TransformNormalToRef(c._CameraDirection,this._invertMatrixTemp,this._cameraDirectionTemp),this._cameraDirectionTemp.normalize(),!isNaN(this._cameraDirectionTemp.x)&&!isNaN(this._cameraDirectionTemp.y)&&!isNaN(this._cameraDirectionTemp.z)&&(this._cachedCameraDirection.equals(this._cameraDirectionTemp)||(this._cachedCameraDirection.copyFrom(this._cameraDirectionTemp),i.audioContext.listener.setOrientation(this._cameraDirectionTemp.x,this._cameraDirectionTemp.y,this._cameraDirectionTemp.z,0,1,0)))):i.audioContext.listener.setOrientation(0,0,0,0,1,0);let o;for(o=0;o<t.mainSoundTrack.soundCollection.length;o++){const r=t.mainSoundTrack.soundCollection[o];r.useCustomAttenuation&&r.updateDistanceFromListener()}if(t.soundTracks)for(o=0;o<t.soundTracks.length;o++)for(let r=0;r<t.soundTracks[o].soundCollection.length;r++){const u=t.soundTracks[o].soundCollection[r];u.useCustomAttenuation&&u.updateDistanceFromListener()}}}}c._CameraDirection=new m(0,0,-1);g._SceneComponentInitialization=a=>{let e=a._getComponent(_.NAME_AUDIO);e||(e=new c(a),a._addComponent(e))};const P="MSFT_audio_emitter";class I{constructor(e){this.name=P,this._loader=e,this.enabled=this._loader.isExtensionUsed(P)}dispose(){this._loader=null,this._clips=null,this._emitters=null}onLoading(){const e=this._loader.gltf.extensions;if(e&&e[this.name]){const t=e[this.name];this._clips=t.clips,this._emitters=t.emitters,A.Assign(this._clips),A.Assign(this._emitters)}}loadSceneAsync(e,t){return b.LoadExtensionAsync(e,t,this.name,(i,s)=>{const o=new Array;o.push(this._loader.loadSceneAsync(e,t));for(const r of s.emitters){const u=A.Get(`${i}/emitters`,this._emitters,r);if(u.refDistance!=null||u.maxDistance!=null||u.rolloffFactor!=null||u.distanceModel!=null||u.innerAngle!=null||u.outerAngle!=null)throw new Error(`${i}: Direction or Distance properties are not allowed on emitters attached to a scene`);o.push(this._loadEmitterAsync(`${i}/emitters/${u.index}`,u))}return Promise.all(o).then(()=>{})})}loadNodeAsync(e,t,i){return b.LoadExtensionAsync(e,t,this.name,(s,o)=>{const r=new Array;return this._loader.loadNodeAsync(s,t,u=>{for(const d of o.emitters){const l=A.Get(`${s}/emitters`,this._emitters,d);r.push(this._loadEmitterAsync(`${s}/emitters/${l.index}`,l).then(()=>{for(const h of l._babylonSounds)h.attachToMesh(u),(l.innerAngle!=null||l.outerAngle!=null)&&(h.setLocalDirectionToMesh(m.Forward()),h.setDirectionalCone(2*T.ToDegrees(l.innerAngle==null?Math.PI:l.innerAngle),2*T.ToDegrees(l.outerAngle==null?Math.PI:l.outerAngle),0))}))}i(u)}).then(u=>Promise.all(r).then(()=>u))})}loadAnimationAsync(e,t){return b.LoadExtensionAsync(e,t,this.name,(i,s)=>this._loader.loadAnimationAsync(e,t).then(o=>{const r=new Array;A.Assign(s.events);for(const u of s.events)r.push(this._loadAnimationEventAsync(`${i}/events/${u.index}`,e,t,u,o));return Promise.all(r).then(()=>o)}))}_loadClipAsync(e,t){if(t._objectURL)return t._objectURL;let i;if(t.uri)i=this._loader.loadUriAsync(e,t,t.uri);else{const s=A.Get(`${e}/bufferView`,this._loader.gltf.bufferViews,t.bufferView);i=this._loader.loadBufferViewAsync(`/bufferViews/${s.index}`,s)}return t._objectURL=i.then(s=>URL.createObjectURL(new Blob([s],{type:t.mimeType}))),t._objectURL}_loadEmitterAsync(e,t){if(t._babylonSounds=t._babylonSounds||[],!t._babylonData){const i=new Array,s=t.name||`emitter${t.index}`,o={loop:!1,autoplay:!1,volume:t.volume==null?1:t.volume};for(let u=0;u<t.clips.length;u++){const d=`/extensions/${this.name}/clips`,l=A.Get(d,this._clips,t.clips[u].clip);i.push(this._loadClipAsync(`${d}/${t.clips[u].clip}`,l).then(h=>{const p=t._babylonSounds[u]=new g(s,h,this._loader.babylonScene,null,o);p.refDistance=t.refDistance||1,p.maxDistance=t.maxDistance||256,p.rolloffFactor=t.rolloffFactor||1,p.distanceModel=t.distanceModel||"exponential"}))}const r=Promise.all(i).then(()=>{const u=t.clips.map(l=>l.weight||1),d=new R(t.loop||!1,t._babylonSounds,u);t.innerAngle&&(d.directionalConeInnerAngle=2*T.ToDegrees(t.innerAngle)),t.outerAngle&&(d.directionalConeOuterAngle=2*T.ToDegrees(t.outerAngle)),t.volume&&(d.volume=t.volume),t._babylonData.sound=d});t._babylonData={loaded:r}}return t._babylonData.loaded}_getEventAction(e,t,i,s,o){switch(i){case"play":return r=>{const u=(o||0)+(r-s);t.play(u)};case"stop":return()=>{t.stop()};case"pause":return()=>{t.pause()};default:throw new Error(`${e}: Unsupported action ${i}`)}}_loadAnimationEventAsync(e,t,i,s,o){if(o.targetedAnimations.length==0)return Promise.resolve();const r=o.targetedAnimations[0],u=s.emitter,d=A.Get(`/extensions/${this.name}/emitters`,this._emitters,u);return this._loadEmitterAsync(e,d).then(()=>{const l=d._babylonData.sound;if(l){const h=new C(s.time,this._getEventAction(e,l,s.action,s.time,s.startOffset));r.animation.addEvent(h),o.onAnimationGroupEndObservable.add(()=>{l.stop()}),o.onAnimationGroupPauseObservable.add(()=>{l.pause()})}})}}w(P);N(P,!0,a=>new I(a));export{I as MSFT_audio_emitter};
//# sourceMappingURL=MSFT_audio_emitter-Dlzxo--T.js.map
