{"data_mtime": 1751179722, "dep_lines": [5, 6, 7, 8, 9, 388, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10], "dep_prios": [10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["os", "base64", "pathlib", "typing", "jinja2", "datetime", "builtins", "_frozen_importlib", "abc", "jinja2.bccache", "jinja2.environment", "jinja2.ext", "jinja2.loaders", "jinja2.runtime", "typing_extensions"], "hash": "8da4671c81cbee8057dc550563f77e0625eecbea", "id": "extensions.reporting.html_generator", "ignore_all": true, "interface_hash": "a88a9f813c727b06210c074fd54ba1458002b45d", "mtime": 1751085577, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\extensions\\reporting\\html_generator.py", "plugin_data": null, "size": 11853, "suppressed": ["markdown2"], "version_id": "1.15.0"}