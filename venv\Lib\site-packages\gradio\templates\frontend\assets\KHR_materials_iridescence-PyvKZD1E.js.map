{"version": 3, "file": "KHR_materials_iridescence-PyvKZD1E.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_iridescence.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_iridescence\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_iridescence/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_iridescence {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 195;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadIridescencePropertiesAsync(extensionContext, extension, babylonMaterial));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadIridescencePropertiesAsync(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        babylonMaterial.iridescence.isEnabled = true;\n        babylonMaterial.iridescence.intensity = properties.iridescenceFactor ?? 0;\n        babylonMaterial.iridescence.indexOfRefraction = properties.iridescenceIor ?? properties.iridescenceIOR ?? 1.3;\n        babylonMaterial.iridescence.minimumThickness = properties.iridescenceThicknessMinimum ?? 100;\n        babylonMaterial.iridescence.maximumThickness = properties.iridescenceThicknessMaximum ?? 400;\n        if (properties.iridescenceTexture) {\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/iridescenceTexture`, properties.iridescenceTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Iridescence)`;\n                babylonMaterial.iridescence.texture = texture;\n            }));\n        }\n        if (properties.iridescenceThicknessTexture) {\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/iridescenceThicknessTexture`, properties.iridescenceThicknessTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Iridescence Thickness)`;\n                babylonMaterial.iridescence.thicknessTexture = texture;\n            }));\n        }\n        return Promise.all(promises).then(() => { });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_iridescence(loader));\n//# sourceMappingURL=KHR_materials_iridescence.js.map"], "names": ["NAME", "KHR_materials_iridescence", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "properties", "PBRMaterial", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "gTAGA,MAAMA,EAAO,4BAKN,MAAMC,CAA0B,CAInC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,gCAAgCF,EAAkBC,EAAWH,CAAe,CAAC,EACzF,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,gCAAgCN,EAASO,EAAYL,EAAiB,CAClE,GAAI,EAAEA,aAA2BM,GAC7B,MAAM,IAAI,MAAM,GAAGR,CAAO,+BAA+B,EAE7D,MAAMM,EAAW,IAAI,MACrB,OAAAJ,EAAgB,YAAY,UAAY,GACxCA,EAAgB,YAAY,UAAYK,EAAW,mBAAqB,EACxEL,EAAgB,YAAY,kBAAoBK,EAAW,gBAAkBA,EAAW,gBAAkB,IAC1GL,EAAgB,YAAY,iBAAmBK,EAAW,6BAA+B,IACzFL,EAAgB,YAAY,iBAAmBK,EAAW,6BAA+B,IACrFA,EAAW,oBACXD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,sBAAuBO,EAAW,mBAAqBE,GAAY,CACzHA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,iBACtCA,EAAgB,YAAY,QAAUO,CACzC,CAAA,CAAC,EAEFF,EAAW,6BACXD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,+BAAgCO,EAAW,4BAA8BE,GAAY,CAC3IA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,2BACtCA,EAAgB,YAAY,iBAAmBO,CAClD,CAAA,CAAC,EAEC,QAAQ,IAAIH,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CAC9C,CACL,CACAI,EAAwBb,CAAI,EAC5Bc,EAAsBd,EAAM,GAAOE,GAAW,IAAID,EAA0BC,CAAM,CAAC", "x_google_ignoreList": [0]}