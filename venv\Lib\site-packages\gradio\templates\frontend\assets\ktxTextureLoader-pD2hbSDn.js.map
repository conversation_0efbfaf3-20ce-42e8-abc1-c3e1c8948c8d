{"version": 3, "file": "ktxTextureLoader-pD2hbSDn.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/khronosTextureContainer.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/ktx2decoderTypes.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/khronosTextureContainer2Worker.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/khronosTextureContainer2.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/ktxTextureLoader.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\nimport { Logger } from \"../Misc/logger.js\";\n/**\n * for description see https://www.khronos.org/opengles/sdk/tools/KTX/\n * for file layout see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/\n */\nexport class KhronosTextureContainer {\n    /**\n     * Creates a new KhronosTextureContainer\n     * @param data contents of the KTX container file\n     * @param facesExpected should be either 1 or 6, based whether a cube texture or or\n     */\n    constructor(\n    /** contents of the KTX container file */\n    data, facesExpected) {\n        this.data = data;\n        /**\n         * If the container has been made invalid (eg. constructor failed to correctly load array buffer)\n         */\n        this.isInvalid = false;\n        if (!KhronosTextureContainer.IsValid(data)) {\n            this.isInvalid = true;\n            Logger.Error(\"texture missing KTX identifier\");\n            return;\n        }\n        // load the reset of the header in native 32 bit uint\n        const dataSize = Uint32Array.BYTES_PER_ELEMENT;\n        const headerDataView = new DataView(this.data.buffer, this.data.byteOffset + 12, 13 * dataSize);\n        const endianness = headerDataView.getUint32(0, true);\n        const littleEndian = endianness === 0x04030201;\n        this.glType = headerDataView.getUint32(1 * dataSize, littleEndian); // must be 0 for compressed textures\n        this.glTypeSize = headerDataView.getUint32(2 * dataSize, littleEndian); // must be 1 for compressed textures\n        this.glFormat = headerDataView.getUint32(3 * dataSize, littleEndian); // must be 0 for compressed textures\n        this.glInternalFormat = headerDataView.getUint32(4 * dataSize, littleEndian); // the value of arg passed to gl.compressedTexImage2D(,,x,,,,)\n        this.glBaseInternalFormat = headerDataView.getUint32(5 * dataSize, littleEndian); // specify GL_RGB, GL_RGBA, GL_ALPHA, etc (un-compressed only)\n        this.pixelWidth = headerDataView.getUint32(6 * dataSize, littleEndian); // level 0 value of arg passed to gl.compressedTexImage2D(,,,x,,,)\n        this.pixelHeight = headerDataView.getUint32(7 * dataSize, littleEndian); // level 0 value of arg passed to gl.compressedTexImage2D(,,,,x,,)\n        this.pixelDepth = headerDataView.getUint32(8 * dataSize, littleEndian); // level 0 value of arg passed to gl.compressedTexImage3D(,,,,,x,,)\n        this.numberOfArrayElements = headerDataView.getUint32(9 * dataSize, littleEndian); // used for texture arrays\n        this.numberOfFaces = headerDataView.getUint32(10 * dataSize, littleEndian); // used for cubemap textures, should either be 1 or 6\n        this.numberOfMipmapLevels = headerDataView.getUint32(11 * dataSize, littleEndian); // number of levels; disregard possibility of 0 for compressed textures\n        this.bytesOfKeyValueData = headerDataView.getUint32(12 * dataSize, littleEndian); // the amount of space after the header for meta-data\n        // Make sure we have a compressed type.  Not only reduces work, but probably better to let dev know they are not compressing.\n        if (this.glType !== 0) {\n            Logger.Error(\"only compressed formats currently supported\");\n            this.isInvalid = true;\n            return;\n        }\n        else {\n            // value of zero is an indication to generate mipmaps @ runtime.  Not usually allowed for compressed, so disregard.\n            this.numberOfMipmapLevels = Math.max(1, this.numberOfMipmapLevels);\n        }\n        if (this.pixelHeight === 0 || this.pixelDepth !== 0) {\n            Logger.Error(\"only 2D textures currently supported\");\n            this.isInvalid = true;\n            return;\n        }\n        if (this.numberOfArrayElements !== 0) {\n            Logger.Error(\"texture arrays not currently supported\");\n            this.isInvalid = true;\n            return;\n        }\n        if (this.numberOfFaces !== facesExpected) {\n            Logger.Error(\"number of faces expected\" + facesExpected + \", but found \" + this.numberOfFaces);\n            this.isInvalid = true;\n            return;\n        }\n        // we now have a completely validated file, so could use existence of loadType as success\n        // would need to make this more elaborate & adjust checks above to support more than one load type\n        this.loadType = KhronosTextureContainer.COMPRESSED_2D;\n    }\n    /**\n     * Uploads KTX content to a Babylon Texture.\n     * It is assumed that the texture has already been created & is currently bound\n     * @internal\n     */\n    uploadLevels(texture, loadMipmaps) {\n        switch (this.loadType) {\n            case KhronosTextureContainer.COMPRESSED_2D:\n                this._upload2DCompressedLevels(texture, loadMipmaps);\n                break;\n            case KhronosTextureContainer.TEX_2D:\n            case KhronosTextureContainer.COMPRESSED_3D:\n            case KhronosTextureContainer.TEX_3D:\n        }\n    }\n    _upload2DCompressedLevels(texture, loadMipmaps) {\n        // initialize width & height for level 1\n        let dataOffset = KhronosTextureContainer.HEADER_LEN + this.bytesOfKeyValueData;\n        let width = this.pixelWidth;\n        let height = this.pixelHeight;\n        const mipmapCount = loadMipmaps ? this.numberOfMipmapLevels : 1;\n        for (let level = 0; level < mipmapCount; level++) {\n            const imageSize = new Int32Array(this.data.buffer, this.data.byteOffset + dataOffset, 1)[0]; // size per face, since not supporting array cubemaps\n            dataOffset += 4; //image data starts from next multiple of 4 offset. Each face refers to same imagesize field above.\n            for (let face = 0; face < this.numberOfFaces; face++) {\n                const byteArray = new Uint8Array(this.data.buffer, this.data.byteOffset + dataOffset, imageSize);\n                const engine = texture.getEngine();\n                engine._uploadCompressedDataToTextureDirectly(texture, texture.format, width, height, byteArray, face, level);\n                dataOffset += imageSize; // add size of the image for the next face/mipmap\n                dataOffset += 3 - ((imageSize + 3) % 4); // add padding for odd sized image\n            }\n            width = Math.max(1.0, width * 0.5);\n            height = Math.max(1.0, height * 0.5);\n        }\n    }\n    /**\n     * Checks if the given data starts with a KTX file identifier.\n     * @param data the data to check\n     * @returns true if the data is a KTX file or false otherwise\n     */\n    static IsValid(data) {\n        if (data.byteLength >= 12) {\n            // '«', 'K', 'T', 'X', ' ', '1', '1', '»', '\\r', '\\n', '\\x1A', '\\n'\n            const identifier = new Uint8Array(data.buffer, data.byteOffset, 12);\n            if (identifier[0] === 0xab &&\n                identifier[1] === 0x4b &&\n                identifier[2] === 0x54 &&\n                identifier[3] === 0x58 &&\n                identifier[4] === 0x20 &&\n                identifier[5] === 0x31 &&\n                identifier[6] === 0x31 &&\n                identifier[7] === 0xbb &&\n                identifier[8] === 0x0d &&\n                identifier[9] === 0x0a &&\n                identifier[10] === 0x1a &&\n                identifier[11] === 0x0a) {\n                return true;\n            }\n        }\n        return false;\n    }\n}\nKhronosTextureContainer.HEADER_LEN = 12 + 13 * 4; // identifier + header elements (not including key value meta-data pairs)\n// load types\nKhronosTextureContainer.COMPRESSED_2D = 0; // uses a gl.compressedTexImage2D()\nKhronosTextureContainer.COMPRESSED_3D = 1; // uses a gl.compressedTexImage3D()\nKhronosTextureContainer.TEX_2D = 2; // uses a gl.texImage2D()\nKhronosTextureContainer.TEX_3D = 3; // uses a gl.texImage3D()\n//# sourceMappingURL=khronosTextureContainer.js.map", "export var SourceTextureFormat;\n(function (SourceTextureFormat) {\n    SourceTextureFormat[SourceTextureFormat[\"ETC1S\"] = 0] = \"ETC1S\";\n    SourceTextureFormat[SourceTextureFormat[\"UASTC4x4\"] = 1] = \"UASTC4x4\";\n})(SourceTextureFormat || (SourceTextureFormat = {}));\nexport var TranscodeTarget;\n(function (TranscodeTarget) {\n    TranscodeTarget[TranscodeTarget[\"ASTC_4X4_RGBA\"] = 0] = \"ASTC_4X4_RGBA\";\n    TranscodeTarget[TranscodeTarget[\"BC7_RGBA\"] = 1] = \"BC7_RGBA\";\n    TranscodeTarget[TranscodeTarget[\"BC3_RGBA\"] = 2] = \"BC3_RGBA\";\n    TranscodeTarget[TranscodeTarget[\"BC1_RGB\"] = 3] = \"BC1_RGB\";\n    TranscodeTarget[TranscodeTarget[\"PVRTC1_4_RGBA\"] = 4] = \"PVRTC1_4_RGBA\";\n    TranscodeTarget[TranscodeTarget[\"PVRTC1_4_RGB\"] = 5] = \"PVRTC1_4_RGB\";\n    TranscodeTarget[TranscodeTarget[\"ETC2_RGBA\"] = 6] = \"ETC2_RGBA\";\n    TranscodeTarget[TranscodeTarget[\"ETC1_RGB\"] = 7] = \"ETC1_RGB\";\n    TranscodeTarget[TranscodeTarget[\"RGBA32\"] = 8] = \"RGBA32\";\n    TranscodeTarget[TranscodeTarget[\"R8\"] = 9] = \"R8\";\n    TranscodeTarget[TranscodeTarget[\"RG8\"] = 10] = \"RG8\";\n})(TranscodeTarget || (TranscodeTarget = {}));\nexport var EngineFormat;\n(function (EngineFormat) {\n    EngineFormat[EngineFormat[\"COMPRESSED_RGBA_BPTC_UNORM_EXT\"] = 36492] = \"COMPRESSED_RGBA_BPTC_UNORM_EXT\";\n    EngineFormat[EngineFormat[\"COMPRESSED_RGBA_ASTC_4X4_KHR\"] = 37808] = \"COMPRESSED_RGBA_ASTC_4X4_KHR\";\n    EngineFormat[EngineFormat[\"COMPRESSED_RGB_S3TC_DXT1_EXT\"] = 33776] = \"COMPRESSED_RGB_S3TC_DXT1_EXT\";\n    EngineFormat[EngineFormat[\"COMPRESSED_RGBA_S3TC_DXT5_EXT\"] = 33779] = \"COMPRESSED_RGBA_S3TC_DXT5_EXT\";\n    EngineFormat[EngineFormat[\"COMPRESSED_RGBA_PVRTC_4BPPV1_IMG\"] = 35842] = \"COMPRESSED_RGBA_PVRTC_4BPPV1_IMG\";\n    EngineFormat[EngineFormat[\"COMPRESSED_RGB_PVRTC_4BPPV1_IMG\"] = 35840] = \"COMPRESSED_RGB_PVRTC_4BPPV1_IMG\";\n    EngineFormat[EngineFormat[\"COMPRESSED_RGBA8_ETC2_EAC\"] = 37496] = \"COMPRESSED_RGBA8_ETC2_EAC\";\n    EngineFormat[EngineFormat[\"COMPRESSED_RGB8_ETC2\"] = 37492] = \"COMPRESSED_RGB8_ETC2\";\n    EngineFormat[EngineFormat[\"COMPRESSED_RGB_ETC1_WEBGL\"] = 36196] = \"COMPRESSED_RGB_ETC1_WEBGL\";\n    EngineFormat[EngineFormat[\"RGBA8Format\"] = 32856] = \"RGBA8Format\";\n    EngineFormat[EngineFormat[\"R8Format\"] = 33321] = \"R8Format\";\n    EngineFormat[EngineFormat[\"RG8Format\"] = 33323] = \"RG8Format\";\n})(EngineFormat || (EngineFormat = {}));\n//# sourceMappingURL=ktx2decoderTypes.js.map", "export function applyConfig(urls, binariesAndModulesContainer) {\n    const KTX2DecoderModule = binariesAndModulesContainer?.jsDecoderModule || KTX2DECODER;\n    if (urls) {\n        if (urls.wasmUASTCToASTC) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_ASTC.WasmModuleURL = urls.wasmUASTCToASTC;\n        }\n        if (urls.wasmUASTCToBC7) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_BC7.WasmModuleURL = urls.wasmUASTCToBC7;\n        }\n        if (urls.wasmUASTCToRGBA_UNORM) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_RGBA_UNORM.WasmModuleURL = urls.wasmUASTCToRGBA_UNORM;\n        }\n        if (urls.wasmUASTCToRGBA_SRGB) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_RGBA_SRGB.WasmModuleURL = urls.wasmUASTCToRGBA_SRGB;\n        }\n        if (urls.wasmUASTCToR8_UNORM) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_R8_UNORM.WasmModuleURL = urls.wasmUASTCToR8_UNORM;\n        }\n        if (urls.wasmUASTCToRG8_UNORM) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_RG8_UNORM.WasmModuleURL = urls.wasmUASTCToRG8_UNORM;\n        }\n        if (urls.jsMSCTranscoder) {\n            KTX2DecoderModule.MSCTranscoder.JSModuleURL = urls.jsMSCTranscoder;\n        }\n        if (urls.wasmMSCTranscoder) {\n            KTX2DecoderModule.MSCTranscoder.WasmModuleURL = urls.wasmMSCTranscoder;\n        }\n        if (urls.wasmZSTDDecoder) {\n            KTX2DecoderModule.ZSTDDecoder.WasmModuleURL = urls.wasmZSTDDecoder;\n        }\n    }\n    if (binariesAndModulesContainer) {\n        if (binariesAndModulesContainer.wasmUASTCToASTC) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_ASTC.WasmBinary = binariesAndModulesContainer.wasmUASTCToASTC;\n        }\n        if (binariesAndModulesContainer.wasmUASTCToBC7) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_BC7.WasmBinary = binariesAndModulesContainer.wasmUASTCToBC7;\n        }\n        if (binariesAndModulesContainer.wasmUASTCToRGBA_UNORM) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_RGBA_UNORM.WasmBinary = binariesAndModulesContainer.wasmUASTCToRGBA_UNORM;\n        }\n        if (binariesAndModulesContainer.wasmUASTCToRGBA_SRGB) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_RGBA_SRGB.WasmBinary = binariesAndModulesContainer.wasmUASTCToRGBA_SRGB;\n        }\n        if (binariesAndModulesContainer.wasmUASTCToR8_UNORM) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_R8_UNORM.WasmBinary = binariesAndModulesContainer.wasmUASTCToR8_UNORM;\n        }\n        if (binariesAndModulesContainer.wasmUASTCToRG8_UNORM) {\n            KTX2DecoderModule.LiteTranscoder_UASTC_RG8_UNORM.WasmBinary = binariesAndModulesContainer.wasmUASTCToRG8_UNORM;\n        }\n        if (binariesAndModulesContainer.jsMSCTranscoder) {\n            KTX2DecoderModule.MSCTranscoder.JSModule = binariesAndModulesContainer.jsMSCTranscoder;\n        }\n        if (binariesAndModulesContainer.wasmMSCTranscoder) {\n            KTX2DecoderModule.MSCTranscoder.WasmBinary = binariesAndModulesContainer.wasmMSCTranscoder;\n        }\n        if (binariesAndModulesContainer.wasmZSTDDecoder) {\n            KTX2DecoderModule.ZSTDDecoder.WasmBinary = binariesAndModulesContainer.wasmZSTDDecoder;\n        }\n    }\n}\nexport function workerFunction(KTX2DecoderModule) {\n    if (typeof KTX2DecoderModule === \"undefined\" && typeof KTX2DECODER !== \"undefined\") {\n        KTX2DecoderModule = KTX2DECODER;\n    }\n    let ktx2Decoder;\n    onmessage = (event) => {\n        if (!event.data) {\n            return;\n        }\n        switch (event.data.action) {\n            case \"init\": {\n                const urls = event.data.urls;\n                if (urls) {\n                    if (urls.jsDecoderModule && typeof KTX2DecoderModule === \"undefined\") {\n                        importScripts(urls.jsDecoderModule);\n                        // assuming global namespace populated by the script (UMD pattern)\n                        KTX2DecoderModule = KTX2DECODER;\n                    }\n                    applyConfig(urls);\n                }\n                if (event.data.wasmBinaries) {\n                    applyConfig(undefined, { ...event.data.wasmBinaries, jsDecoderModule: KTX2DecoderModule });\n                }\n                ktx2Decoder = new KTX2DecoderModule.KTX2Decoder();\n                postMessage({ action: \"init\" });\n                break;\n            }\n            case \"setDefaultDecoderOptions\": {\n                KTX2DecoderModule.KTX2Decoder.DefaultDecoderOptions = event.data.options;\n                break;\n            }\n            case \"decode\":\n                ktx2Decoder\n                    .decode(event.data.data, event.data.caps, event.data.options)\n                    .then((data) => {\n                    const buffers = [];\n                    for (let mip = 0; mip < data.mipmaps.length; ++mip) {\n                        const mipmap = data.mipmaps[mip];\n                        if (mipmap && mipmap.data) {\n                            buffers.push(mipmap.data.buffer);\n                        }\n                    }\n                    postMessage({ action: \"decoded\", success: true, decodedData: data }, buffers);\n                })\n                    .catch((reason) => {\n                    postMessage({ action: \"decoded\", success: false, msg: reason });\n                });\n                break;\n        }\n    };\n}\nexport function initializeWebWorker(worker, wasmBinaries, urls) {\n    return new Promise((resolve, reject) => {\n        const onError = (error) => {\n            worker.removeEventListener(\"error\", onError);\n            worker.removeEventListener(\"message\", onMessage);\n            reject(error);\n        };\n        const onMessage = (message) => {\n            if (message.data.action === \"init\") {\n                worker.removeEventListener(\"error\", onError);\n                worker.removeEventListener(\"message\", onMessage);\n                resolve(worker);\n            }\n        };\n        worker.addEventListener(\"error\", onError);\n        worker.addEventListener(\"message\", onMessage);\n        worker.postMessage({\n            action: \"init\",\n            urls,\n            wasmBinaries,\n        });\n    });\n}\n//# sourceMappingURL=khronosTextureContainer2Worker.js.map", "\nimport { AutoReleaseWorkerPool } from \"./workerPool.js\";\nimport { Tools } from \"./tools.js\";\nimport { TranscodeTarget } from \"../Materials/Textures/ktx2decoderTypes.js\";\nimport { applyConfig, initializeWeb<PERSON>orker, workerFunction } from \"./khronosTextureContainer2Worker.js\";\n/**\n * Class that defines the default KTX2 decoder options.\n *\n * This class is useful for providing options to the KTX2 decoder to control how the source data is transcoded.\n */\nexport class DefaultKTX2DecoderOptions {\n    constructor() {\n        this._isDirty = true;\n        this._useRGBAIfOnlyBC1BC3AvailableWhenUASTC = true;\n        this._ktx2DecoderOptions = {};\n    }\n    /**\n     * Gets the dirty flag\n     */\n    get isDirty() {\n        return this._isDirty;\n    }\n    /**\n     * force a (uncompressed) RGBA transcoded format if transcoding a UASTC source format and ASTC + BC7 are not available as a compressed transcoded format\n     */\n    get useRGBAIfASTCBC7NotAvailableWhenUASTC() {\n        return this._useRGBAIfASTCBC7NotAvailableWhenUASTC;\n    }\n    set useRGBAIfASTCBC7NotAvailableWhenUASTC(value) {\n        if (this._useRGBAIfASTCBC7NotAvailableWhenUASTC === value) {\n            return;\n        }\n        this._useRGBAIfASTCBC7NotAvailableWhenUASTC = value;\n        this._isDirty = true;\n    }\n    /**\n     * force a (uncompressed) RGBA transcoded format if transcoding a UASTC source format and only BC1 or BC3 are available as a compressed transcoded format.\n     * This property is true by default to favor speed over memory, because currently transcoding from UASTC to BC1/3 is slow because the transcoder transcodes\n     * to uncompressed and then recompresses the texture\n     */\n    get useRGBAIfOnlyBC1BC3AvailableWhenUASTC() {\n        return this._useRGBAIfOnlyBC1BC3AvailableWhenUASTC;\n    }\n    set useRGBAIfOnlyBC1BC3AvailableWhenUASTC(value) {\n        if (this._useRGBAIfOnlyBC1BC3AvailableWhenUASTC === value) {\n            return;\n        }\n        this._useRGBAIfOnlyBC1BC3AvailableWhenUASTC = value;\n        this._isDirty = true;\n    }\n    /**\n     * force to always use (uncompressed) RGBA for transcoded format\n     */\n    get forceRGBA() {\n        return this._forceRGBA;\n    }\n    set forceRGBA(value) {\n        if (this._forceRGBA === value) {\n            return;\n        }\n        this._forceRGBA = value;\n        this._isDirty = true;\n    }\n    /**\n     * force to always use (uncompressed) R8 for transcoded format\n     */\n    get forceR8() {\n        return this._forceR8;\n    }\n    set forceR8(value) {\n        if (this._forceR8 === value) {\n            return;\n        }\n        this._forceR8 = value;\n        this._isDirty = true;\n    }\n    /**\n     * force to always use (uncompressed) RG8 for transcoded format\n     */\n    get forceRG8() {\n        return this._forceRG8;\n    }\n    set forceRG8(value) {\n        if (this._forceRG8 === value) {\n            return;\n        }\n        this._forceRG8 = value;\n        this._isDirty = true;\n    }\n    /**\n     * list of transcoders to bypass when looking for a suitable transcoder. The available transcoders are:\n     *      UniversalTranscoder_UASTC_ASTC\n     *      UniversalTranscoder_UASTC_BC7\n     *      UniversalTranscoder_UASTC_RGBA_UNORM\n     *      UniversalTranscoder_UASTC_RGBA_SRGB\n     *      UniversalTranscoder_UASTC_R8_UNORM\n     *      UniversalTranscoder_UASTC_RG8_UNORM\n     *      MSCTranscoder\n     */\n    get bypassTranscoders() {\n        return this._bypassTranscoders;\n    }\n    set bypassTranscoders(value) {\n        if (this._bypassTranscoders === value) {\n            return;\n        }\n        this._bypassTranscoders = value;\n        this._isDirty = true;\n    }\n    /** @internal */\n    _getKTX2DecoderOptions() {\n        if (!this._isDirty) {\n            return this._ktx2DecoderOptions;\n        }\n        this._isDirty = false;\n        const options = {\n            useRGBAIfASTCBC7NotAvailableWhenUASTC: this._useRGBAIfASTCBC7NotAvailableWhenUASTC,\n            forceRGBA: this._forceRGBA,\n            forceR8: this._forceR8,\n            forceRG8: this._forceRG8,\n            bypassTranscoders: this._bypassTranscoders,\n        };\n        if (this.useRGBAIfOnlyBC1BC3AvailableWhenUASTC) {\n            options.transcodeFormatDecisionTree = {\n                UASTC: {\n                    transcodeFormat: [TranscodeTarget.BC1_RGB, TranscodeTarget.BC3_RGBA],\n                    yes: {\n                        transcodeFormat: TranscodeTarget.RGBA32,\n                        engineFormat: 32856 /* EngineFormat.RGBA8Format */,\n                        roundToMultiple4: false,\n                    },\n                },\n            };\n        }\n        this._ktx2DecoderOptions = options;\n        return options;\n    }\n}\n/**\n * Class for loading KTX2 files\n */\nexport class KhronosTextureContainer2 {\n    static GetDefaultNumWorkers() {\n        if (typeof navigator !== \"object\" || !navigator.hardwareConcurrency) {\n            return 1;\n        }\n        // Use 50% of the available logical processors but capped at 4.\n        return Math.min(Math.floor(navigator.hardwareConcurrency * 0.5), 4);\n    }\n    static _Initialize(numWorkers) {\n        if (KhronosTextureContainer2._WorkerPoolPromise || KhronosTextureContainer2._DecoderModulePromise) {\n            return;\n        }\n        const urls = {\n            jsDecoderModule: Tools.GetBabylonScriptURL(this.URLConfig.jsDecoderModule, true),\n            wasmUASTCToASTC: Tools.GetBabylonScriptURL(this.URLConfig.wasmUASTCToASTC, true),\n            wasmUASTCToBC7: Tools.GetBabylonScriptURL(this.URLConfig.wasmUASTCToBC7, true),\n            wasmUASTCToRGBA_UNORM: Tools.GetBabylonScriptURL(this.URLConfig.wasmUASTCToRGBA_UNORM, true),\n            wasmUASTCToRGBA_SRGB: Tools.GetBabylonScriptURL(this.URLConfig.wasmUASTCToRGBA_SRGB, true),\n            wasmUASTCToR8_UNORM: Tools.GetBabylonScriptURL(this.URLConfig.wasmUASTCToR8_UNORM, true),\n            wasmUASTCToRG8_UNORM: Tools.GetBabylonScriptURL(this.URLConfig.wasmUASTCToRG8_UNORM, true),\n            jsMSCTranscoder: Tools.GetBabylonScriptURL(this.URLConfig.jsMSCTranscoder, true),\n            wasmMSCTranscoder: Tools.GetBabylonScriptURL(this.URLConfig.wasmMSCTranscoder, true),\n            wasmZSTDDecoder: Tools.GetBabylonScriptURL(this.URLConfig.wasmZSTDDecoder, true),\n        };\n        if (numWorkers && typeof Worker === \"function\" && typeof URL !== \"undefined\") {\n            KhronosTextureContainer2._WorkerPoolPromise = new Promise((resolve) => {\n                const workerContent = `${applyConfig}(${workerFunction})()`;\n                const workerBlobUrl = URL.createObjectURL(new Blob([workerContent], { type: \"application/javascript\" }));\n                resolve(new AutoReleaseWorkerPool(numWorkers, () => initializeWebWorker(new Worker(workerBlobUrl), undefined, urls)));\n            });\n        }\n        else {\n            if (typeof KhronosTextureContainer2._KTX2DecoderModule === \"undefined\") {\n                KhronosTextureContainer2._DecoderModulePromise = Tools.LoadBabylonScriptAsync(urls.jsDecoderModule).then(() => {\n                    KhronosTextureContainer2._KTX2DecoderModule = KTX2DECODER;\n                    KhronosTextureContainer2._KTX2DecoderModule.MSCTranscoder.UseFromWorkerThread = false;\n                    KhronosTextureContainer2._KTX2DecoderModule.WASMMemoryManager.LoadBinariesFromCurrentThread = true;\n                    applyConfig(urls, KhronosTextureContainer2._KTX2DecoderModule);\n                    return new KhronosTextureContainer2._KTX2DecoderModule.KTX2Decoder();\n                });\n            }\n            else {\n                KhronosTextureContainer2._KTX2DecoderModule.MSCTranscoder.UseFromWorkerThread = false;\n                KhronosTextureContainer2._KTX2DecoderModule.WASMMemoryManager.LoadBinariesFromCurrentThread = true;\n                KhronosTextureContainer2._DecoderModulePromise = Promise.resolve(new KhronosTextureContainer2._KTX2DecoderModule.KTX2Decoder());\n            }\n        }\n    }\n    /**\n     * Constructor\n     * @param engine The engine to use\n     * @param numWorkersOrOptions The number of workers for async operations. Specify `0` to disable web workers and run synchronously in the current context.\n     */\n    constructor(engine, numWorkersOrOptions = KhronosTextureContainer2.DefaultNumWorkers) {\n        this._engine = engine;\n        const workerPoolOption = (typeof numWorkersOrOptions === \"object\" && numWorkersOrOptions.workerPool) || KhronosTextureContainer2.WorkerPool;\n        if (workerPoolOption) {\n            KhronosTextureContainer2._WorkerPoolPromise = Promise.resolve(workerPoolOption);\n        }\n        else {\n            // set the KTX2 decoder module\n            if (typeof numWorkersOrOptions === \"object\") {\n                KhronosTextureContainer2._KTX2DecoderModule = numWorkersOrOptions?.binariesAndModulesContainer?.jsDecoderModule;\n            }\n            else if (typeof KTX2DECODER !== \"undefined\") {\n                KhronosTextureContainer2._KTX2DecoderModule = KTX2DECODER;\n            }\n            const numberOfWorkers = typeof numWorkersOrOptions === \"number\" ? numWorkersOrOptions : (numWorkersOrOptions.numWorkers ?? KhronosTextureContainer2.DefaultNumWorkers);\n            KhronosTextureContainer2._Initialize(numberOfWorkers);\n        }\n    }\n    /**\n     * @internal\n     */\n    _uploadAsync(data, internalTexture, options) {\n        const caps = this._engine.getCaps();\n        const compressedTexturesCaps = {\n            astc: !!caps.astc,\n            bptc: !!caps.bptc,\n            s3tc: !!caps.s3tc,\n            pvrtc: !!caps.pvrtc,\n            etc2: !!caps.etc2,\n            etc1: !!caps.etc1,\n        };\n        if (KhronosTextureContainer2._WorkerPoolPromise) {\n            return KhronosTextureContainer2._WorkerPoolPromise.then((workerPool) => {\n                return new Promise((resolve, reject) => {\n                    workerPool.push((worker, onComplete) => {\n                        const onError = (error) => {\n                            worker.removeEventListener(\"error\", onError);\n                            worker.removeEventListener(\"message\", onMessage);\n                            reject(error);\n                            onComplete();\n                        };\n                        const onMessage = (message) => {\n                            if (message.data.action === \"decoded\") {\n                                worker.removeEventListener(\"error\", onError);\n                                worker.removeEventListener(\"message\", onMessage);\n                                if (!message.data.success) {\n                                    reject({ message: message.data.msg });\n                                }\n                                else {\n                                    try {\n                                        this._createTexture(message.data.decodedData, internalTexture, options);\n                                        resolve();\n                                    }\n                                    catch (err) {\n                                        reject({ message: err });\n                                    }\n                                }\n                                onComplete();\n                            }\n                        };\n                        worker.addEventListener(\"error\", onError);\n                        worker.addEventListener(\"message\", onMessage);\n                        worker.postMessage({ action: \"setDefaultDecoderOptions\", options: KhronosTextureContainer2.DefaultDecoderOptions._getKTX2DecoderOptions() });\n                        const dataCopy = new Uint8Array(data.byteLength);\n                        dataCopy.set(new Uint8Array(data.buffer, data.byteOffset, data.byteLength));\n                        worker.postMessage({ action: \"decode\", data: dataCopy, caps: compressedTexturesCaps, options }, [dataCopy.buffer]);\n                    });\n                });\n            });\n        }\n        else if (KhronosTextureContainer2._DecoderModulePromise) {\n            return KhronosTextureContainer2._DecoderModulePromise.then((decoder) => {\n                if (KhronosTextureContainer2.DefaultDecoderOptions.isDirty) {\n                    KhronosTextureContainer2._KTX2DecoderModule.KTX2Decoder.DefaultDecoderOptions = KhronosTextureContainer2.DefaultDecoderOptions._getKTX2DecoderOptions();\n                }\n                return new Promise((resolve, reject) => {\n                    decoder\n                        .decode(data, caps)\n                        .then((data) => {\n                        this._createTexture(data, internalTexture);\n                        resolve();\n                    })\n                        .catch((reason) => {\n                        reject({ message: reason });\n                    });\n                });\n            });\n        }\n        throw new Error(\"KTX2 decoder module is not available\");\n    }\n    _createTexture(data, internalTexture, options) {\n        const oglTexture2D = 3553; // gl.TEXTURE_2D\n        this._engine._bindTextureDirectly(oglTexture2D, internalTexture);\n        if (options) {\n            // return back some information about the decoded data\n            options.transcodedFormat = data.transcodedFormat;\n            options.isInGammaSpace = data.isInGammaSpace;\n            options.hasAlpha = data.hasAlpha;\n            options.transcoderName = data.transcoderName;\n        }\n        let isUncompressedFormat = true;\n        switch (data.transcodedFormat) {\n            case 0x8058 /* RGBA8 */:\n                internalTexture.type = 0;\n                internalTexture.format = 5;\n                break;\n            case 0x8229 /* R8 */:\n                internalTexture.type = 0;\n                internalTexture.format = 6;\n                break;\n            case 0x822b /* RG8 */:\n                internalTexture.type = 0;\n                internalTexture.format = 7;\n                break;\n            default:\n                internalTexture.format = data.transcodedFormat;\n                isUncompressedFormat = false;\n                break;\n        }\n        internalTexture._gammaSpace = data.isInGammaSpace;\n        internalTexture.generateMipMaps = data.mipmaps.length > 1;\n        if (data.errors) {\n            throw new Error(\"KTX2 container - could not transcode the data. \" + data.errors);\n        }\n        for (let t = 0; t < data.mipmaps.length; ++t) {\n            const mipmap = data.mipmaps[t];\n            if (!mipmap || !mipmap.data) {\n                throw new Error(\"KTX2 container - could not transcode one of the image\");\n            }\n            if (isUncompressedFormat) {\n                // uncompressed RGBA / R8 / RG8\n                internalTexture.width = mipmap.width; // need to set width/height so that the call to _uploadDataToTextureDirectly uses the right dimensions\n                internalTexture.height = mipmap.height;\n                this._engine._uploadDataToTextureDirectly(internalTexture, mipmap.data, 0, t, undefined, true);\n            }\n            else {\n                this._engine._uploadCompressedDataToTextureDirectly(internalTexture, data.transcodedFormat, mipmap.width, mipmap.height, mipmap.data, 0, t);\n            }\n        }\n        internalTexture._extension = \".ktx2\";\n        internalTexture.width = data.mipmaps[0].width;\n        internalTexture.height = data.mipmaps[0].height;\n        internalTexture.isReady = true;\n        this._engine._bindTextureDirectly(oglTexture2D, null);\n    }\n    /**\n     * Checks if the given data starts with a KTX2 file identifier.\n     * @param data the data to check\n     * @returns true if the data is a KTX2 file or false otherwise\n     */\n    static IsValid(data) {\n        if (data.byteLength >= 12) {\n            // '«', 'K', 'T', 'X', ' ', '2', '0', '»', '\\r', '\\n', '\\x1A', '\\n'\n            const identifier = new Uint8Array(data.buffer, data.byteOffset, 12);\n            if (identifier[0] === 0xab &&\n                identifier[1] === 0x4b &&\n                identifier[2] === 0x54 &&\n                identifier[3] === 0x58 &&\n                identifier[4] === 0x20 &&\n                identifier[5] === 0x32 &&\n                identifier[6] === 0x30 &&\n                identifier[7] === 0xbb &&\n                identifier[8] === 0x0d &&\n                identifier[9] === 0x0a &&\n                identifier[10] === 0x1a &&\n                identifier[11] === 0x0a) {\n                return true;\n            }\n        }\n        return false;\n    }\n}\n/**\n * URLs to use when loading the KTX2 decoder module as well as its dependencies\n * If a url is null, the default url is used (pointing to https://preview.babylonjs.com)\n * Note that jsDecoderModule can't be null and that the other dependencies will only be loaded if necessary\n * Urls you can change:\n *     URLConfig.jsDecoderModule\n *     URLConfig.wasmUASTCToASTC\n *     URLConfig.wasmUASTCToBC7\n *     URLConfig.wasmUASTCToRGBA_UNORM\n *     URLConfig.wasmUASTCToRGBA_SRGB\n *     URLConfig.wasmUASTCToR8_UNORM\n *     URLConfig.wasmUASTCToRG8_UNORM\n *     URLConfig.jsMSCTranscoder\n *     URLConfig.wasmMSCTranscoder\n *     URLConfig.wasmZSTDDecoder\n * You can see their default values in this PG: https://playground.babylonjs.com/#EIJH8L#29\n */\nKhronosTextureContainer2.URLConfig = {\n    jsDecoderModule: \"https://cdn.babylonjs.com/babylon.ktx2Decoder.js\",\n    wasmUASTCToASTC: null,\n    wasmUASTCToBC7: null,\n    wasmUASTCToRGBA_UNORM: null,\n    wasmUASTCToRGBA_SRGB: null,\n    wasmUASTCToR8_UNORM: null,\n    wasmUASTCToRG8_UNORM: null,\n    jsMSCTranscoder: null,\n    wasmMSCTranscoder: null,\n    wasmZSTDDecoder: null,\n};\n/**\n * Default number of workers used to handle data decoding\n */\nKhronosTextureContainer2.DefaultNumWorkers = KhronosTextureContainer2.GetDefaultNumWorkers();\n/**\n * Default configuration for the KTX2 decoder.\n * The options defined in this way have priority over those passed when creating a KTX2 texture with new Texture(...).\n */\nKhronosTextureContainer2.DefaultDecoderOptions = new DefaultKTX2DecoderOptions();\n//# sourceMappingURL=khronosTextureContainer2.js.map", "import { KhronosTextureContainer } from \"../../../Misc/khronosTextureContainer.js\";\nimport { KhronosTextureContainer2 } from \"../../../Misc/khronosTextureContainer2.js\";\nimport { Logger } from \"../../../Misc/logger.js\";\n\nfunction mapSRGBToLinear(format) {\n    switch (format) {\n        case 35916:\n            return 33776;\n        case 35918:\n            return 33778;\n        case 35919:\n            return 33779;\n        case 37493:\n            return 37492;\n        case 37497:\n            return 37496;\n        case 37495:\n            return 37494;\n        case 37840:\n            return 37808;\n        case 36493:\n            return 36492;\n    }\n    return null;\n}\n/**\n * Implementation of the KTX Texture Loader.\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class _KTXTextureLoader {\n    constructor() {\n        /**\n         * Defines whether the loader supports cascade loading the different faces.\n         */\n        this.supportCascades = false;\n    }\n    /**\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param createPolynomials will be true if polynomials have been requested\n     * @param onLoad defines the callback to trigger once the texture is ready\n     */\n    loadCubeData(data, texture, createPolynomials, onLoad) {\n        if (Array.isArray(data)) {\n            return;\n        }\n        // Need to invert vScale as invertY via UNPACK_FLIP_Y_WEBGL is not supported by compressed texture\n        texture._invertVScale = !texture.invertY;\n        const engine = texture.getEngine();\n        const ktx = new KhronosTextureContainer(data, 6);\n        const loadMipmap = ktx.numberOfMipmapLevels > 1 && texture.generateMipMaps;\n        engine._unpackFlipY(true);\n        ktx.uploadLevels(texture, texture.generateMipMaps);\n        texture.width = ktx.pixelWidth;\n        texture.height = ktx.pixelHeight;\n        engine._setCubeMapTextureParams(texture, loadMipmap, ktx.numberOfMipmapLevels - 1);\n        texture.isReady = true;\n        texture.onLoadedObservable.notifyObservers(texture);\n        texture.onLoadedObservable.clear();\n        if (onLoad) {\n            onLoad();\n        }\n    }\n    /**\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param callback defines the method to call once ready to upload\n     * @param options\n     */\n    loadData(data, texture, callback, options) {\n        if (KhronosTextureContainer.IsValid(data)) {\n            // Need to invert vScale as invertY via UNPACK_FLIP_Y_WEBGL is not supported by compressed texture\n            texture._invertVScale = !texture.invertY;\n            const ktx = new KhronosTextureContainer(data, 1);\n            const mappedFormat = mapSRGBToLinear(ktx.glInternalFormat);\n            if (mappedFormat) {\n                texture.format = mappedFormat;\n                texture._useSRGBBuffer = texture.getEngine()._getUseSRGBBuffer(true, texture.generateMipMaps);\n                texture._gammaSpace = true;\n            }\n            else {\n                texture.format = ktx.glInternalFormat;\n            }\n            callback(ktx.pixelWidth, ktx.pixelHeight, texture.generateMipMaps, true, () => {\n                ktx.uploadLevels(texture, texture.generateMipMaps);\n            }, ktx.isInvalid);\n        }\n        else if (KhronosTextureContainer2.IsValid(data)) {\n            const ktx2 = new KhronosTextureContainer2(texture.getEngine());\n            ktx2._uploadAsync(data, texture, options).then(() => {\n                callback(texture.width, texture.height, texture.generateMipMaps, true, () => { }, false);\n            }, (error) => {\n                Logger.Warn(`Failed to load KTX2 texture data: ${error.message}`);\n                callback(0, 0, false, false, () => { }, true);\n            });\n        }\n        else {\n            Logger.Error(\"texture missing KTX identifier\");\n            callback(0, 0, false, false, () => { }, true);\n        }\n    }\n}\n//# sourceMappingURL=ktxTextureLoader.js.map"], "names": ["KhronosTextureContainer", "data", "facesExpected", "<PERSON><PERSON>", "dataSize", "headerDataView", "littleEndian", "texture", "loadMipmaps", "dataOffset", "width", "height", "mipmapCount", "level", "imageSize", "face", "byteArray", "identifier", "SourceTextureFormat", "TranscodeTarget", "EngineFormat", "applyConfig", "urls", "binariesAndModulesContainer", "KTX2DecoderModule", "workerFunction", "ktx2Decoder", "event", "buffers", "mip", "mipmap", "reason", "initializeWebWorker", "worker", "wasmBinaries", "resolve", "reject", "onError", "error", "onMessage", "message", "DefaultKTX2DecoderOptions", "value", "options", "KhronosTextureContainer2", "numWorkers", "Tools", "workerContent", "workerBlobUrl", "AutoReleaseWorkerPool", "engine", "numWorkersOrOptions", "workerPoolOption", "numberOfWorkers", "internalTexture", "caps", "compressedTexturesCaps", "workerPool", "onComplete", "err", "dataCopy", "decoder", "isUncompressedFormat", "t", "mapSRGBToLinear", "format", "_KTXTextureLoader", "createPolynomials", "onLoad", "ktx", "loadMipmap", "callback", "mappedFormat"], "mappings": "mJAMO,MAAMA,CAAwB,CAMjC,YAEAC,EAAMC,EAAe,CAMjB,GALA,KAAK,KAAOD,EAIZ,KAAK,UAAY,GACb,CAACD,EAAwB,QAAQC,CAAI,EAAG,CACxC,KAAK,UAAY,GACjBE,EAAO,MAAM,gCAAgC,EAC7C,MACH,CAED,MAAMC,EAAW,YAAY,kBACvBC,EAAiB,IAAI,SAAS,KAAK,KAAK,OAAQ,KAAK,KAAK,WAAa,GAAI,GAAKD,CAAQ,EAExFE,EADaD,EAAe,UAAU,EAAG,EAAI,IACf,SAcpC,GAbA,KAAK,OAASA,EAAe,UAAU,EAAID,EAAUE,CAAY,EACjE,KAAK,WAAaD,EAAe,UAAU,EAAID,EAAUE,CAAY,EACrE,KAAK,SAAWD,EAAe,UAAU,EAAID,EAAUE,CAAY,EACnE,KAAK,iBAAmBD,EAAe,UAAU,EAAID,EAAUE,CAAY,EAC3E,KAAK,qBAAuBD,EAAe,UAAU,EAAID,EAAUE,CAAY,EAC/E,KAAK,WAAaD,EAAe,UAAU,EAAID,EAAUE,CAAY,EACrE,KAAK,YAAcD,EAAe,UAAU,EAAID,EAAUE,CAAY,EACtE,KAAK,WAAaD,EAAe,UAAU,EAAID,EAAUE,CAAY,EACrE,KAAK,sBAAwBD,EAAe,UAAU,EAAID,EAAUE,CAAY,EAChF,KAAK,cAAgBD,EAAe,UAAU,GAAKD,EAAUE,CAAY,EACzE,KAAK,qBAAuBD,EAAe,UAAU,GAAKD,EAAUE,CAAY,EAChF,KAAK,oBAAsBD,EAAe,UAAU,GAAKD,EAAUE,CAAY,EAE3E,KAAK,SAAW,EAAG,CACnBH,EAAO,MAAM,6CAA6C,EAC1D,KAAK,UAAY,GACjB,MACH,MAGG,KAAK,qBAAuB,KAAK,IAAI,EAAG,KAAK,oBAAoB,EAErE,GAAI,KAAK,cAAgB,GAAK,KAAK,aAAe,EAAG,CACjDA,EAAO,MAAM,sCAAsC,EACnD,KAAK,UAAY,GACjB,MACH,CACD,GAAI,KAAK,wBAA0B,EAAG,CAClCA,EAAO,MAAM,wCAAwC,EACrD,KAAK,UAAY,GACjB,MACH,CACD,GAAI,KAAK,gBAAkBD,EAAe,CACtCC,EAAO,MAAM,2BAA6BD,EAAgB,eAAiB,KAAK,aAAa,EAC7F,KAAK,UAAY,GACjB,MACH,CAGD,KAAK,SAAWF,EAAwB,aAC3C,CAMD,aAAaO,EAASC,EAAa,CAC/B,OAAQ,KAAK,SAAQ,CACjB,KAAKR,EAAwB,cACzB,KAAK,0BAA0BO,EAASC,CAAW,EACnD,KAIP,CACJ,CACD,0BAA0BD,EAASC,EAAa,CAE5C,IAAIC,EAAaT,EAAwB,WAAa,KAAK,oBACvDU,EAAQ,KAAK,WACbC,EAAS,KAAK,YAClB,MAAMC,EAAcJ,EAAc,KAAK,qBAAuB,EAC9D,QAASK,EAAQ,EAAGA,EAAQD,EAAaC,IAAS,CAC9C,MAAMC,EAAY,IAAI,WAAW,KAAK,KAAK,OAAQ,KAAK,KAAK,WAAaL,EAAY,CAAC,EAAE,CAAC,EAC1FA,GAAc,EACd,QAASM,EAAO,EAAGA,EAAO,KAAK,cAAeA,IAAQ,CAClD,MAAMC,EAAY,IAAI,WAAW,KAAK,KAAK,OAAQ,KAAK,KAAK,WAAaP,EAAYK,CAAS,EAChFP,EAAQ,YAChB,uCAAuCA,EAASA,EAAQ,OAAQG,EAAOC,EAAQK,EAAWD,EAAMF,CAAK,EAC5GJ,GAAcK,EACdL,GAAc,GAAMK,EAAY,GAAK,CACxC,CACDJ,EAAQ,KAAK,IAAI,EAAKA,EAAQ,EAAG,EACjCC,EAAS,KAAK,IAAI,EAAKA,EAAS,EAAG,CACtC,CACJ,CAMD,OAAO,QAAQV,EAAM,CACjB,GAAIA,EAAK,YAAc,GAAI,CAEvB,MAAMgB,EAAa,IAAI,WAAWhB,EAAK,OAAQA,EAAK,WAAY,EAAE,EAClE,GAAIgB,EAAW,CAAC,IAAM,KAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,KAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,EAAE,IAAM,IACnBA,EAAW,EAAE,IAAM,GACnB,MAAO,EAEd,CACD,MAAO,EACV,CACL,CACAjB,EAAwB,WAAa,GAAK,GAAK,EAE/CA,EAAwB,cAAgB,EACxCA,EAAwB,cAAgB,EACxCA,EAAwB,OAAS,EACjCA,EAAwB,OAAS,EC1I1B,IAAIkB,GACV,SAAUA,EAAqB,CAC5BA,EAAoBA,EAAoB,MAAW,CAAC,EAAI,QACxDA,EAAoBA,EAAoB,SAAc,CAAC,EAAI,UAC/D,GAAGA,IAAwBA,EAAsB,CAAE,EAAC,EAC7C,IAAIC,GACV,SAAUA,EAAiB,CACxBA,EAAgBA,EAAgB,cAAmB,CAAC,EAAI,gBACxDA,EAAgBA,EAAgB,SAAc,CAAC,EAAI,WACnDA,EAAgBA,EAAgB,SAAc,CAAC,EAAI,WACnDA,EAAgBA,EAAgB,QAAa,CAAC,EAAI,UAClDA,EAAgBA,EAAgB,cAAmB,CAAC,EAAI,gBACxDA,EAAgBA,EAAgB,aAAkB,CAAC,EAAI,eACvDA,EAAgBA,EAAgB,UAAe,CAAC,EAAI,YACpDA,EAAgBA,EAAgB,SAAc,CAAC,EAAI,WACnDA,EAAgBA,EAAgB,OAAY,CAAC,EAAI,SACjDA,EAAgBA,EAAgB,GAAQ,CAAC,EAAI,KAC7CA,EAAgBA,EAAgB,IAAS,EAAE,EAAI,KACnD,GAAGA,IAAoBA,EAAkB,CAAE,EAAC,EACrC,IAAIC,GACV,SAAUA,EAAc,CACrBA,EAAaA,EAAa,+BAAoC,KAAK,EAAI,iCACvEA,EAAaA,EAAa,6BAAkC,KAAK,EAAI,+BACrEA,EAAaA,EAAa,6BAAkC,KAAK,EAAI,+BACrEA,EAAaA,EAAa,8BAAmC,KAAK,EAAI,gCACtEA,EAAaA,EAAa,iCAAsC,KAAK,EAAI,mCACzEA,EAAaA,EAAa,gCAAqC,KAAK,EAAI,kCACxEA,EAAaA,EAAa,0BAA+B,KAAK,EAAI,4BAClEA,EAAaA,EAAa,qBAA0B,KAAK,EAAI,uBAC7DA,EAAaA,EAAa,0BAA+B,KAAK,EAAI,4BAClEA,EAAaA,EAAa,YAAiB,KAAK,EAAI,cACpDA,EAAaA,EAAa,SAAc,KAAK,EAAI,WACjDA,EAAaA,EAAa,UAAe,KAAK,EAAI,WACtD,GAAGA,IAAiBA,EAAe,CAAA,EAAG,ECjC/B,SAASC,EAAYC,EAAMC,EAA6B,CAC3D,MAAMC,EAAoBD,GAA6B,iBAAmB,YACtED,IACIA,EAAK,kBACLE,EAAkB,0BAA0B,cAAgBF,EAAK,iBAEjEA,EAAK,iBACLE,EAAkB,yBAAyB,cAAgBF,EAAK,gBAEhEA,EAAK,wBACLE,EAAkB,gCAAgC,cAAgBF,EAAK,uBAEvEA,EAAK,uBACLE,EAAkB,+BAA+B,cAAgBF,EAAK,sBAEtEA,EAAK,sBACLE,EAAkB,8BAA8B,cAAgBF,EAAK,qBAErEA,EAAK,uBACLE,EAAkB,+BAA+B,cAAgBF,EAAK,sBAEtEA,EAAK,kBACLE,EAAkB,cAAc,YAAcF,EAAK,iBAEnDA,EAAK,oBACLE,EAAkB,cAAc,cAAgBF,EAAK,mBAErDA,EAAK,kBACLE,EAAkB,YAAY,cAAgBF,EAAK,kBAGvDC,IACIA,EAA4B,kBAC5BC,EAAkB,0BAA0B,WAAaD,EAA4B,iBAErFA,EAA4B,iBAC5BC,EAAkB,yBAAyB,WAAaD,EAA4B,gBAEpFA,EAA4B,wBAC5BC,EAAkB,gCAAgC,WAAaD,EAA4B,uBAE3FA,EAA4B,uBAC5BC,EAAkB,+BAA+B,WAAaD,EAA4B,sBAE1FA,EAA4B,sBAC5BC,EAAkB,8BAA8B,WAAaD,EAA4B,qBAEzFA,EAA4B,uBAC5BC,EAAkB,+BAA+B,WAAaD,EAA4B,sBAE1FA,EAA4B,kBAC5BC,EAAkB,cAAc,SAAWD,EAA4B,iBAEvEA,EAA4B,oBAC5BC,EAAkB,cAAc,WAAaD,EAA4B,mBAEzEA,EAA4B,kBAC5BC,EAAkB,YAAY,WAAaD,EAA4B,iBAGnF,CACO,SAASE,EAAeD,EAAmB,CAC1C,OAAOA,EAAsB,KAAe,OAAO,YAAgB,MACnEA,EAAoB,aAExB,IAAIE,EACJ,UAAaC,GAAU,CACnB,GAAKA,EAAM,KAGX,OAAQA,EAAM,KAAK,OAAM,CACrB,IAAK,OAAQ,CACT,MAAML,EAAOK,EAAM,KAAK,KACpBL,IACIA,EAAK,iBAAmB,OAAOE,EAAsB,MACrD,cAAcF,EAAK,eAAe,EAElCE,EAAoB,aAExBH,EAAYC,CAAI,GAEhBK,EAAM,KAAK,cACXN,EAAY,OAAW,CAAE,GAAGM,EAAM,KAAK,aAAc,gBAAiBH,CAAiB,CAAE,EAE7FE,EAAc,IAAIF,EAAkB,YACpC,YAAY,CAAE,OAAQ,MAAM,CAAE,EAC9B,KACH,CACD,IAAK,2BAA4B,CAC7BA,EAAkB,YAAY,sBAAwBG,EAAM,KAAK,QACjE,KACH,CACD,IAAK,SACDD,EACK,OAAOC,EAAM,KAAK,KAAMA,EAAM,KAAK,KAAMA,EAAM,KAAK,OAAO,EAC3D,KAAM1B,GAAS,CAChB,MAAM2B,EAAU,CAAA,EAChB,QAASC,EAAM,EAAGA,EAAM5B,EAAK,QAAQ,OAAQ,EAAE4B,EAAK,CAChD,MAAMC,EAAS7B,EAAK,QAAQ4B,CAAG,EAC3BC,GAAUA,EAAO,MACjBF,EAAQ,KAAKE,EAAO,KAAK,MAAM,CAEtC,CACD,YAAY,CAAE,OAAQ,UAAW,QAAS,GAAM,YAAa7B,GAAQ2B,CAAO,CAChG,CAAiB,EACI,MAAOG,GAAW,CACnB,YAAY,CAAE,OAAQ,UAAW,QAAS,GAAO,IAAKA,CAAM,CAAE,CAClF,CAAiB,EACD,KACP,CACT,CACA,CACO,SAASC,EAAoBC,EAAQC,EAAcZ,EAAM,CAC5D,OAAO,IAAI,QAAQ,CAACa,EAASC,IAAW,CACpC,MAAMC,EAAWC,GAAU,CACvBL,EAAO,oBAAoB,QAASI,CAAO,EAC3CJ,EAAO,oBAAoB,UAAWM,CAAS,EAC/CH,EAAOE,CAAK,CACxB,EACcC,EAAaC,GAAY,CACvBA,EAAQ,KAAK,SAAW,SACxBP,EAAO,oBAAoB,QAASI,CAAO,EAC3CJ,EAAO,oBAAoB,UAAWM,CAAS,EAC/CJ,EAAQF,CAAM,EAE9B,EACQA,EAAO,iBAAiB,QAASI,CAAO,EACxCJ,EAAO,iBAAiB,UAAWM,CAAS,EAC5CN,EAAO,YAAY,CACf,OAAQ,OACR,KAAAX,EACA,aAAAY,CACZ,CAAS,CACT,CAAK,CACL,CC5HO,MAAMO,CAA0B,CACnC,aAAc,CACV,KAAK,SAAW,GAChB,KAAK,uCAAyC,GAC9C,KAAK,oBAAsB,EAC9B,CAID,IAAI,SAAU,CACV,OAAO,KAAK,QACf,CAID,IAAI,uCAAwC,CACxC,OAAO,KAAK,sCACf,CACD,IAAI,sCAAsCC,EAAO,CACzC,KAAK,yCAA2CA,IAGpD,KAAK,uCAAyCA,EAC9C,KAAK,SAAW,GACnB,CAMD,IAAI,uCAAwC,CACxC,OAAO,KAAK,sCACf,CACD,IAAI,sCAAsCA,EAAO,CACzC,KAAK,yCAA2CA,IAGpD,KAAK,uCAAyCA,EAC9C,KAAK,SAAW,GACnB,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CACD,IAAI,UAAUA,EAAO,CACb,KAAK,aAAeA,IAGxB,KAAK,WAAaA,EAClB,KAAK,SAAW,GACnB,CAID,IAAI,SAAU,CACV,OAAO,KAAK,QACf,CACD,IAAI,QAAQA,EAAO,CACX,KAAK,WAAaA,IAGtB,KAAK,SAAWA,EAChB,KAAK,SAAW,GACnB,CAID,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CACD,IAAI,SAASA,EAAO,CACZ,KAAK,YAAcA,IAGvB,KAAK,UAAYA,EACjB,KAAK,SAAW,GACnB,CAWD,IAAI,mBAAoB,CACpB,OAAO,KAAK,kBACf,CACD,IAAI,kBAAkBA,EAAO,CACrB,KAAK,qBAAuBA,IAGhC,KAAK,mBAAqBA,EAC1B,KAAK,SAAW,GACnB,CAED,wBAAyB,CACrB,GAAI,CAAC,KAAK,SACN,OAAO,KAAK,oBAEhB,KAAK,SAAW,GAChB,MAAMC,EAAU,CACZ,sCAAuC,KAAK,uCAC5C,UAAW,KAAK,WAChB,QAAS,KAAK,SACd,SAAU,KAAK,UACf,kBAAmB,KAAK,kBACpC,EACQ,OAAI,KAAK,wCACLA,EAAQ,4BAA8B,CAClC,MAAO,CACH,gBAAiB,CAACxB,EAAgB,QAASA,EAAgB,QAAQ,EACnE,IAAK,CACD,gBAAiBA,EAAgB,OACjC,aAAc,MACd,iBAAkB,EACrB,CACJ,CACjB,GAEQ,KAAK,oBAAsBwB,EACpBA,CACV,CACL,CAIO,MAAMC,CAAyB,CAClC,OAAO,sBAAuB,CAC1B,OAAI,OAAO,WAAc,UAAY,CAAC,UAAU,oBACrC,EAGJ,KAAK,IAAI,KAAK,MAAM,UAAU,oBAAsB,EAAG,EAAG,CAAC,CACrE,CACD,OAAO,YAAYC,EAAY,CAC3B,GAAID,EAAyB,oBAAsBA,EAAyB,sBACxE,OAEJ,MAAMtB,EAAO,CACT,gBAAiBwB,EAAM,oBAAoB,KAAK,UAAU,gBAAiB,EAAI,EAC/E,gBAAiBA,EAAM,oBAAoB,KAAK,UAAU,gBAAiB,EAAI,EAC/E,eAAgBA,EAAM,oBAAoB,KAAK,UAAU,eAAgB,EAAI,EAC7E,sBAAuBA,EAAM,oBAAoB,KAAK,UAAU,sBAAuB,EAAI,EAC3F,qBAAsBA,EAAM,oBAAoB,KAAK,UAAU,qBAAsB,EAAI,EACzF,oBAAqBA,EAAM,oBAAoB,KAAK,UAAU,oBAAqB,EAAI,EACvF,qBAAsBA,EAAM,oBAAoB,KAAK,UAAU,qBAAsB,EAAI,EACzF,gBAAiBA,EAAM,oBAAoB,KAAK,UAAU,gBAAiB,EAAI,EAC/E,kBAAmBA,EAAM,oBAAoB,KAAK,UAAU,kBAAmB,EAAI,EACnF,gBAAiBA,EAAM,oBAAoB,KAAK,UAAU,gBAAiB,EAAI,CAC3F,EACYD,GAAc,OAAO,QAAW,YAAc,OAAO,IAAQ,IAC7DD,EAAyB,mBAAqB,IAAI,QAAST,GAAY,CACnE,MAAMY,EAAgB,GAAG1B,CAAW,IAAII,CAAc,MAChDuB,EAAgB,IAAI,gBAAgB,IAAI,KAAK,CAACD,CAAa,EAAG,CAAE,KAAM,wBAAwB,CAAE,CAAC,EACvGZ,EAAQ,IAAIc,EAAsBJ,EAAY,IAAMb,EAAoB,IAAI,OAAOgB,CAAa,EAAG,OAAW1B,CAAI,CAAC,CAAC,CACpI,CAAa,EAGG,OAAOsB,EAAyB,mBAAuB,IACvDA,EAAyB,sBAAwBE,EAAM,uBAAuBxB,EAAK,eAAe,EAAE,KAAK,KACrGsB,EAAyB,mBAAqB,YAC9CA,EAAyB,mBAAmB,cAAc,oBAAsB,GAChFA,EAAyB,mBAAmB,kBAAkB,8BAAgC,GAC9FvB,EAAYC,EAAMsB,EAAyB,kBAAkB,EACtD,IAAIA,EAAyB,mBAAmB,YAC1D,GAGDA,EAAyB,mBAAmB,cAAc,oBAAsB,GAChFA,EAAyB,mBAAmB,kBAAkB,8BAAgC,GAC9FA,EAAyB,sBAAwB,QAAQ,QAAQ,IAAIA,EAAyB,mBAAmB,WAAa,EAGzI,CAMD,YAAYM,EAAQC,EAAsBP,EAAyB,kBAAmB,CAClF,KAAK,QAAUM,EACf,MAAME,EAAoB,OAAOD,GAAwB,UAAYA,EAAoB,YAAeP,EAAyB,WACjI,GAAIQ,EACAR,EAAyB,mBAAqB,QAAQ,QAAQQ,CAAgB,MAE7E,CAEG,OAAOD,GAAwB,SAC/BP,EAAyB,mBAAqBO,GAAqB,6BAA6B,gBAE3F,OAAO,YAAgB,MAC5BP,EAAyB,mBAAqB,aAElD,MAAMS,EAAkB,OAAOF,GAAwB,SAAWA,EAAuBA,EAAoB,YAAcP,EAAyB,kBACpJA,EAAyB,YAAYS,CAAe,CACvD,CACJ,CAID,aAAapD,EAAMqD,EAAiBX,EAAS,CACzC,MAAMY,EAAO,KAAK,QAAQ,QAAO,EAC3BC,EAAyB,CAC3B,KAAM,CAAC,CAACD,EAAK,KACb,KAAM,CAAC,CAACA,EAAK,KACb,KAAM,CAAC,CAACA,EAAK,KACb,MAAO,CAAC,CAACA,EAAK,MACd,KAAM,CAAC,CAACA,EAAK,KACb,KAAM,CAAC,CAACA,EAAK,IACzB,EACQ,GAAIX,EAAyB,mBACzB,OAAOA,EAAyB,mBAAmB,KAAMa,GAC9C,IAAI,QAAQ,CAACtB,EAASC,IAAW,CACpCqB,EAAW,KAAK,CAACxB,EAAQyB,IAAe,CACpC,MAAMrB,EAAWC,GAAU,CACvBL,EAAO,oBAAoB,QAASI,CAAO,EAC3CJ,EAAO,oBAAoB,UAAWM,CAAS,EAC/CH,EAAOE,CAAK,EACZoB,GAC5B,EAC8BnB,EAAaC,GAAY,CAC3B,GAAIA,EAAQ,KAAK,SAAW,UAAW,CAGnC,GAFAP,EAAO,oBAAoB,QAASI,CAAO,EAC3CJ,EAAO,oBAAoB,UAAWM,CAAS,EAC3C,CAACC,EAAQ,KAAK,QACdJ,EAAO,CAAE,QAASI,EAAQ,KAAK,GAAK,CAAA,MAGpC,IAAI,CACA,KAAK,eAAeA,EAAQ,KAAK,YAAac,EAAiBX,CAAO,EACtER,GACH,OACMwB,EAAK,CACRvB,EAAO,CAAE,QAASuB,CAAG,CAAE,CAC1B,CAELD,GACH,CAC7B,EACwBzB,EAAO,iBAAiB,QAASI,CAAO,EACxCJ,EAAO,iBAAiB,UAAWM,CAAS,EAC5CN,EAAO,YAAY,CAAE,OAAQ,2BAA4B,QAASW,EAAyB,sBAAsB,uBAAwB,CAAA,CAAE,EAC3I,MAAMgB,EAAW,IAAI,WAAW3D,EAAK,UAAU,EAC/C2D,EAAS,IAAI,IAAI,WAAW3D,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,CAAC,EAC1EgC,EAAO,YAAY,CAAE,OAAQ,SAAU,KAAM2B,EAAU,KAAMJ,EAAwB,QAAAb,CAAS,EAAE,CAACiB,EAAS,MAAM,CAAC,CACzI,CAAqB,CACrB,CAAiB,CACJ,EAEA,GAAIhB,EAAyB,sBAC9B,OAAOA,EAAyB,sBAAsB,KAAMiB,IACpDjB,EAAyB,sBAAsB,UAC/CA,EAAyB,mBAAmB,YAAY,sBAAwBA,EAAyB,sBAAsB,0BAE5H,IAAI,QAAQ,CAACT,EAASC,IAAW,CACpCyB,EACK,OAAO5D,EAAMsD,CAAI,EACjB,KAAMtD,GAAS,CAChB,KAAK,eAAeA,EAAMqD,CAAe,EACzCnB,GACxB,CAAqB,EACI,MAAOJ,GAAW,CACnBK,EAAO,CAAE,QAASL,CAAM,CAAE,CAClD,CAAqB,CACrB,CAAiB,EACJ,EAEL,MAAM,IAAI,MAAM,sCAAsC,CACzD,CACD,eAAe9B,EAAMqD,EAAiBX,EAAS,CAE3C,KAAK,QAAQ,qBAAqB,KAAcW,CAAe,EAC3DX,IAEAA,EAAQ,iBAAmB1C,EAAK,iBAChC0C,EAAQ,eAAiB1C,EAAK,eAC9B0C,EAAQ,SAAW1C,EAAK,SACxB0C,EAAQ,eAAiB1C,EAAK,gBAElC,IAAI6D,EAAuB,GAC3B,OAAQ7D,EAAK,iBAAgB,CACzB,IAAK,OACDqD,EAAgB,KAAO,EACvBA,EAAgB,OAAS,EACzB,MACJ,IAAK,OACDA,EAAgB,KAAO,EACvBA,EAAgB,OAAS,EACzB,MACJ,IAAK,OACDA,EAAgB,KAAO,EACvBA,EAAgB,OAAS,EACzB,MACJ,QACIA,EAAgB,OAASrD,EAAK,iBAC9B6D,EAAuB,GACvB,KACP,CAGD,GAFAR,EAAgB,YAAcrD,EAAK,eACnCqD,EAAgB,gBAAkBrD,EAAK,QAAQ,OAAS,EACpDA,EAAK,OACL,MAAM,IAAI,MAAM,kDAAoDA,EAAK,MAAM,EAEnF,QAAS8D,EAAI,EAAGA,EAAI9D,EAAK,QAAQ,OAAQ,EAAE8D,EAAG,CAC1C,MAAMjC,EAAS7B,EAAK,QAAQ8D,CAAC,EAC7B,GAAI,CAACjC,GAAU,CAACA,EAAO,KACnB,MAAM,IAAI,MAAM,uDAAuD,EAEvEgC,GAEAR,EAAgB,MAAQxB,EAAO,MAC/BwB,EAAgB,OAASxB,EAAO,OAChC,KAAK,QAAQ,6BAA6BwB,EAAiBxB,EAAO,KAAM,EAAGiC,EAAG,OAAW,EAAI,GAG7F,KAAK,QAAQ,uCAAuCT,EAAiBrD,EAAK,iBAAkB6B,EAAO,MAAOA,EAAO,OAAQA,EAAO,KAAM,EAAGiC,CAAC,CAEjJ,CACDT,EAAgB,WAAa,QAC7BA,EAAgB,MAAQrD,EAAK,QAAQ,CAAC,EAAE,MACxCqD,EAAgB,OAASrD,EAAK,QAAQ,CAAC,EAAE,OACzCqD,EAAgB,QAAU,GAC1B,KAAK,QAAQ,qBAAqB,KAAc,IAAI,CACvD,CAMD,OAAO,QAAQrD,EAAM,CACjB,GAAIA,EAAK,YAAc,GAAI,CAEvB,MAAMgB,EAAa,IAAI,WAAWhB,EAAK,OAAQA,EAAK,WAAY,EAAE,EAClE,GAAIgB,EAAW,CAAC,IAAM,KAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,KAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,CAAC,IAAM,IAClBA,EAAW,EAAE,IAAM,IACnBA,EAAW,EAAE,IAAM,GACnB,MAAO,EAEd,CACD,MAAO,EACV,CACL,CAkBA2B,EAAyB,UAAY,CACjC,gBAAiB,mDACjB,gBAAiB,KACjB,eAAgB,KAChB,sBAAuB,KACvB,qBAAsB,KACtB,oBAAqB,KACrB,qBAAsB,KACtB,gBAAiB,KACjB,kBAAmB,KACnB,gBAAiB,IACrB,EAIAA,EAAyB,kBAAoBA,EAAyB,uBAKtEA,EAAyB,sBAAwB,IAAIH,EC/YrD,SAASuB,EAAgBC,EAAQ,CAC7B,OAAQA,EAAM,CACV,IAAK,OACD,MAAO,OACX,IAAK,OACD,MAAO,OACX,IAAK,OACD,MAAO,OACX,IAAK,OACD,MAAO,OACX,IAAK,OACD,MAAO,OACX,IAAK,OACD,MAAO,OACX,IAAK,OACD,MAAO,OACX,IAAK,OACD,MAAO,MACd,CACD,OAAO,IACX,CAMO,MAAMC,CAAkB,CAC3B,aAAc,CAIV,KAAK,gBAAkB,EAC1B,CAQD,aAAajE,EAAMM,EAAS4D,EAAmBC,EAAQ,CACnD,GAAI,MAAM,QAAQnE,CAAI,EAClB,OAGJM,EAAQ,cAAgB,CAACA,EAAQ,QACjC,MAAM2C,EAAS3C,EAAQ,YACjB8D,EAAM,IAAIrE,EAAwBC,EAAM,CAAC,EACzCqE,EAAaD,EAAI,qBAAuB,GAAK9D,EAAQ,gBAC3D2C,EAAO,aAAa,EAAI,EACxBmB,EAAI,aAAa9D,EAASA,EAAQ,eAAe,EACjDA,EAAQ,MAAQ8D,EAAI,WACpB9D,EAAQ,OAAS8D,EAAI,YACrBnB,EAAO,yBAAyB3C,EAAS+D,EAAYD,EAAI,qBAAuB,CAAC,EACjF9D,EAAQ,QAAU,GAClBA,EAAQ,mBAAmB,gBAAgBA,CAAO,EAClDA,EAAQ,mBAAmB,QACvB6D,GACAA,GAEP,CAQD,SAASnE,EAAMM,EAASgE,EAAU5B,EAAS,CACvC,GAAI3C,EAAwB,QAAQC,CAAI,EAAG,CAEvCM,EAAQ,cAAgB,CAACA,EAAQ,QACjC,MAAM8D,EAAM,IAAIrE,EAAwBC,EAAM,CAAC,EACzCuE,EAAeR,EAAgBK,EAAI,gBAAgB,EACrDG,GACAjE,EAAQ,OAASiE,EACjBjE,EAAQ,eAAiBA,EAAQ,UAAS,EAAG,kBAAkB,GAAMA,EAAQ,eAAe,EAC5FA,EAAQ,YAAc,IAGtBA,EAAQ,OAAS8D,EAAI,iBAEzBE,EAASF,EAAI,WAAYA,EAAI,YAAa9D,EAAQ,gBAAiB,GAAM,IAAM,CAC3E8D,EAAI,aAAa9D,EAASA,EAAQ,eAAe,CACjE,EAAe8D,EAAI,SAAS,CACnB,MACQzB,EAAyB,QAAQ3C,CAAI,EAC7B,IAAI2C,EAAyBrC,EAAQ,UAAW,CAAA,EACxD,aAAaN,EAAMM,EAASoC,CAAO,EAAE,KAAK,IAAM,CACjD4B,EAAShE,EAAQ,MAAOA,EAAQ,OAAQA,EAAQ,gBAAiB,GAAM,IAAM,CAAG,EAAE,EAAK,CAC1F,EAAG+B,GAAU,CACVnC,EAAO,KAAK,qCAAqCmC,EAAM,OAAO,EAAE,EAChEiC,EAAS,EAAG,EAAG,GAAO,GAAO,IAAM,CAAA,EAAK,EAAI,CAC5D,CAAa,GAGDpE,EAAO,MAAM,gCAAgC,EAC7CoE,EAAS,EAAG,EAAG,GAAO,GAAO,IAAM,CAAA,EAAK,EAAI,EAEnD,CACL", "x_google_ignoreList": [0, 1, 2, 3, 4]}