{"version": 3, "file": "logDepthVertex-_NzWs2Ji.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneVertexDeclaration.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/fogVertexDeclaration.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneVertex.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/fogVertex.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/logDepthVertex.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"clipPlaneVertexDeclaration\";\nconst shader = `#ifdef CLIPPLANE\nuniform vClipPlane: vec4<f32>;varying fClipDistance: f32;\n#endif\n#ifdef CLIPPLANE2\nuniform vClipPlane2: vec4<f32>;varying fClipDistance2: f32;\n#endif\n#ifdef CLIPPLANE3\nuniform vClipPlane3: vec4<f32>;varying fClipDistance3: f32;\n#endif\n#ifdef CLIPPLANE4\nuniform vClipPlane4: vec4<f32>;varying fClipDistance4: f32;\n#endif\n#ifdef CLIPPLANE5\nuniform vClipPlane5: vec4<f32>;varying fClipDistance5: f32;\n#endif\n#ifdef CLIPPLANE6\nuniform vClipPlane6: vec4<f32>;varying fClipDistance6: f32;\n#endif\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const clipPlaneVertexDeclarationWGSL = { name, shader };\n//# sourceMappingURL=clipPlaneVertexDeclaration.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"fogVertexDeclaration\";\nconst shader = `#ifdef FOG\nvarying vFogDistance: vec3f;\n#endif\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const fogVertexDeclarationWGSL = { name, shader };\n//# sourceMappingURL=fogVertexDeclaration.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"clipPlaneVertex\";\nconst shader = `#ifdef CLIPPLANE\nvertexOutputs.fClipDistance=dot(worldPos,uniforms.vClipPlane);\n#endif\n#ifdef CLIPPLANE2\nvertexOutputs.fClipDistance2=dot(worldPos,uniforms.vClipPlane2);\n#endif\n#ifdef CLIPPLANE3\nvertexOutputs.fClipDistance3=dot(worldPos,uniforms.vClipPlane3);\n#endif\n#ifdef CLIPPLANE4\nvertexOutputs.fClipDistance4=dot(worldPos,uniforms.vClipPlane4);\n#endif\n#ifdef CLIPPLANE5\nvertexOutputs.fClipDistance5=dot(worldPos,uniforms.vClipPlane5);\n#endif\n#ifdef CLIPPLANE6\nvertexOutputs.fClipDistance6=dot(worldPos,uniforms.vClipPlane6);\n#endif\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const clipPlaneVertexWGSL = { name, shader };\n//# sourceMappingURL=clipPlaneVertex.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"fogVertex\";\nconst shader = `#ifdef FOG\n#ifdef SCENE_UBO\nvertexOutputs.vFogDistance=(scene.view*worldPos).xyz;\n#else\nvertexOutputs.vFogDistance=(uniforms.view*worldPos).xyz;\n#endif\n#endif\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const fogVertexWGSL = { name, shader };\n//# sourceMappingURL=fogVertex.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"logDepthVertex\";\nconst shader = `#ifdef LOGARITHMICDEPTH\nvertexOutputs.vFragmentDepth=1.0+vertexOutputs.position.w;vertexOutputs.position.z=log2(max(0.000001,vertexOutputs.vFragmentDepth))*uniforms.logarithmicDepthConstant;\n#endif\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const logDepthVertexWGSL = { name, shader };\n//# sourceMappingURL=logDepthVertex.js.map"], "names": ["name", "shader", "ShaderStore"], "mappings": "wCAEA,MAAMA,EAAO,6BACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC,GCtBjD,MAAMD,EAAO,uBACPC,EAAS;AAAA;AAAA;AAAA,EAKVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC,GCPjD,MAAMD,EAAO,kBACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC,GCtBjD,MAAMD,EAAO,YACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC,GCXjD,MAAMD,EAAO,iBACPC,EAAS;AAAA;AAAA;AAAA,EAKVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC", "x_google_ignoreList": [0, 1, 2, 3, 4]}