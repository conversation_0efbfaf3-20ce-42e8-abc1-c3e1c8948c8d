"""
漏斗图实现
用于检测发表偏倚
"""
from typing import Dict, Any, List, Optional
import numpy as np
import plotly.graph_objects as go
from .base_plot import BasePlot

class FunnelPlot(BasePlot):
    """
    漏斗图类，用于检测发表偏倚
    """
    
    def _create_figure(self) -> go.Figure:
        """
        创建漏斗图
        
        Returns:
            go.Figure: 漏斗图对象
        """
        fig = go.Figure()
        
        # 提取数据
        studies = self.data.get('studies', [])
        if not studies:
            return self._create_empty_plot()
            
        # 添加研究点
        self._add_studies(fig, studies)
        
        # 添加对称线（如果存在汇总效应量）
        if 'summary' in self.data:
            self._add_symmetry_lines(fig, studies, self.data['summary'])
        
        # 更新布局
        self._update_layout(fig)
        
        return fig
    
    def _add_studies(self, fig: go.Figure, studies: List[Dict[str, Any]]) -> None:
        """
        添加研究点到图表
        
        Args:
            fig: Plotly图表对象
            studies: 研究数据列表
        """
        # 准备数据
        effect_sizes = []
        standard_errors = []
        labels = []
        sample_sizes = []
        
        for study in studies:
            effect_sizes.append(study.get('effect_size', 0))
            se = study.get('standard_error', 0)
            standard_errors.append(se if se > 0 else 0.01)  # 避免除零错误
            labels.append(study.get('label', ''))
            sample_sizes.append(study.get('sample_size', 0))
        
        # 添加散点图
        fig.add_trace(go.Scatter(
            x=effect_sizes,
            y=standard_errors,
            mode='markers',
            name='研究',
            text=labels,
            marker=dict(
                color='#1f77b4',
                size=[min(30, 10 + s/100) for s in sample_sizes],
                opacity=0.7,
                line=dict(width=1, color='DarkSlateGrey')
            ),
            customdata=np.column_stack((labels, sample_sizes)),
            hovertemplate=(
                "<b>%{text}</b><br>"
                "效应量: %{x:.2f}<br>"
                "标准误: %{y:.2f}<br>"
                "样本量: %{customdata[1]}<br>"
                "<extra></extra>"
            )
        ))
    
    def _add_symmetry_lines(self, fig: go.Figure, studies: List[Dict[str, Any]], 
                          summary: Dict[str, Any]) -> None:
        """
        添加对称线到漏斗图
        
        Args:
            fig: Plotly图表对象
            studies: 研究数据列表
            summary: 汇总效应量数据
        """
        if not studies:
            return
            
        # 计算汇总效应量和标准误范围
        summary_effect = summary.get('effect_size', 0)
        max_se = max(study.get('standard_error', 0) for study in studies) * 1.1  # 增加10%的边距
        
        if max_se <= 0:
            return
            
        # 创建对称线数据点
        se_range = np.linspace(0.01, max_se, 50)
        
        # 95% 置信区间线
        ci_upper = [summary_effect + 1.96 * se for se in se_range]
        ci_lower = [summary_effect - 1.96 * se for se in se_range]
        
        # 添加汇总效应量线
        fig.add_trace(go.Scatter(
            x=[summary_effect] * len(se_range),
            y=se_range,
            mode='lines',
            name='汇总效应量',
            line=dict(color='red', width=1, dash='dash'),
            showlegend=False
        ))
        
        # 添加上方95% CI线
        fig.add_trace(go.Scatter(
            x=ci_upper,
            y=se_range,
            mode='lines',
            name='95% 置信区间',
            line=dict(color='red', width=1, dash='dot'),
            fill=None,
            showlegend=True
        ))
        
        # 添加下方95% CI线并填充区域
        fig.add_trace(go.Scatter(
            x=ci_lower,
            y=se_range,
            mode='lines',
            line=dict(color='red', width=1, dash='dot'),
            fill='tonexty',
            fillcolor='rgba(255, 0, 0, 0.1)',
            name='95% 置信区间',
            showlegend=False
        ))
        
        # 添加汇总效应量点
        fig.add_trace(go.Scatter(
            x=[summary_effect],
            y=[0],
            mode='markers',
            name='汇总效应量',
            marker=dict(
                color='red',
                size=10,
                symbol='diamond'
            ),
            showlegend=True,
            hovertemplate=(
                "<b>汇总效应量</b><br>"
                f"效应量: {summary_effect:.2f}<br>"
                "<extra></extra>"
            )
        ))
    
    def _update_layout(self, fig: go.Figure) -> None:
        """
        更新图表布局
        
        Args:
            fig: Plotly图表对象
        """
        # 更新布局
        fig.update_layout(
            title=dict(
                text='<b>漏斗图 - 发表偏倚评估</b>',
                x=0.5,
                xanchor='center',
                y=0.95,
                yanchor='top'
            ),
            xaxis=dict(
                title='<b>效应量</b>',
                showgrid=True,
                zeroline=True,
                showline=True,
                linewidth=1,
                linecolor='black',
                mirror=True
            ),
            yaxis=dict(
                title='<b>标准误</b>',
                autorange='reversed',  # 反转Y轴，使漏斗朝下
                showgrid=True,
                showline=True,
                linewidth=1,
                linecolor='black',
                mirror=True
            ),
            legend=dict(
                orientation='h',
                yanchor='bottom',
                y=1.02,
                xanchor='right',
                x=1
            ),
            hovermode='closest',
            plot_bgcolor='white',
            margin=dict(l=50, r=50, t=80, b=50),
            height=600
        )
    
    def _create_empty_plot(self) -> go.Figure:
        """
        创建空图表
        
        Returns:
            go.Figure: 空图表对象
        """
        fig = go.Figure()
        fig.add_annotation(
            text="没有可用的研究数据",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white'
        )
        return fig
