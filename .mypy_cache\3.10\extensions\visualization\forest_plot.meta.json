{"data_mtime": 1751179722, "dep_lines": [8, 5, 6, 1, 1, 1, 7, 7], "dep_prios": [5, 5, 10, 5, 30, 30, 10, 20], "dependencies": ["extensions.visualization.base_plot", "typing", "numpy", "builtins", "_frozen_importlib", "abc"], "hash": "3db3920e27299a2463eae9bfd9281a00572bfac5", "id": "extensions.visualization.forest_plot", "ignore_all": true, "interface_hash": "39b4d7b344f5ac9ded1a13db3281c1257af191e0", "mtime": 1751085465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\extensions\\visualization\\forest_plot.py", "plugin_data": null, "size": 7401, "suppressed": ["plotly.graph_objects", "plotly"], "version_id": "1.15.0"}