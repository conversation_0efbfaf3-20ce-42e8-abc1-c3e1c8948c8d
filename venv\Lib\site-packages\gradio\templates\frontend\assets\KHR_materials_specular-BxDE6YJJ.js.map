{"version": 3, "file": "KHR_materials_specular-BxDE6YJJ.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_specular.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_specular\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_specular/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_specular {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 190;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadSpecularPropertiesAsync(extensionContext, extension, babylonMaterial));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadSpecularPropertiesAsync(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        if (properties.specularFactor !== undefined) {\n            babylonMaterial.metallicF0Factor = properties.specularFactor;\n        }\n        if (properties.specularColorFactor !== undefined) {\n            babylonMaterial.metallicReflectanceColor = Color3.FromArray(properties.specularColorFactor);\n        }\n        if (properties.specularTexture) {\n            properties.specularTexture.nonColorData = true;\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/specularTexture`, properties.specularTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Specular)`;\n                babylonMaterial.metallicReflectanceTexture = texture;\n                babylonMaterial.useOnlyMetallicFromMetallicReflectanceTexture = true;\n            }));\n        }\n        if (properties.specularColorTexture) {\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/specularColorTexture`, properties.specularColorTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Specular Color)`;\n                babylonMaterial.reflectanceTexture = texture;\n            }));\n        }\n        return Promise.all(promises).then(() => { });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_specular(loader));\n//# sourceMappingURL=KHR_materials_specular.js.map"], "names": ["NAME", "KHR_materials_specular", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "properties", "PBRMaterial", "Color3", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "uTAIA,MAAMA,EAAO,yBAKN,MAAMC,CAAuB,CAIhC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,6BAA6BF,EAAkBC,EAAWH,CAAe,CAAC,EACtF,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,6BAA6BN,EAASO,EAAYL,EAAiB,CAC/D,GAAI,EAAEA,aAA2BM,GAC7B,MAAM,IAAI,MAAM,GAAGR,CAAO,+BAA+B,EAE7D,MAAMM,EAAW,IAAI,MACrB,OAAIC,EAAW,iBAAmB,SAC9BL,EAAgB,iBAAmBK,EAAW,gBAE9CA,EAAW,sBAAwB,SACnCL,EAAgB,yBAA2BO,EAAO,UAAUF,EAAW,mBAAmB,GAE1FA,EAAW,kBACXA,EAAW,gBAAgB,aAAe,GAC1CD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,mBAAoBO,EAAW,gBAAkBG,GAAY,CACnHA,EAAQ,KAAO,GAAGR,EAAgB,IAAI,cACtCA,EAAgB,2BAA6BQ,EAC7CR,EAAgB,8CAAgD,EACnE,CAAA,CAAC,GAEFK,EAAW,sBACXD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,wBAAyBO,EAAW,qBAAuBG,GAAY,CAC7HA,EAAQ,KAAO,GAAGR,EAAgB,IAAI,oBACtCA,EAAgB,mBAAqBQ,CACxC,CAAA,CAAC,EAEC,QAAQ,IAAIJ,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CAC9C,CACL,CACAK,EAAwBd,CAAI,EAC5Be,EAAsBf,EAAM,GAAOE,GAAW,IAAID,EAAuBC,CAAM,CAAC", "x_google_ignoreList": [0]}