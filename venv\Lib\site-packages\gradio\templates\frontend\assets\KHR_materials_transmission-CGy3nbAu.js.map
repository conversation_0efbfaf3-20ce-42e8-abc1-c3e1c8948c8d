{"version": 3, "file": "KHR_materials_transmission-CGy3nbAu.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_transmission.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { RenderTargetTexture } from \"@babylonjs/core/Materials/Textures/renderTargetTexture.js\";\nimport { Observable } from \"@babylonjs/core/Misc/observable.js\";\nimport { Constants } from \"@babylonjs/core/Engines/constants.js\";\nimport { Tools } from \"@babylonjs/core/Misc/tools.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\n/**\n * A class to handle setting up the rendering of opaque objects to be shown through transmissive objects.\n */\nclass TransmissionHelper {\n    /**\n     * Creates the default options for the helper.\n     * @returns the default options\n     */\n    static _GetDefaultOptions() {\n        return {\n            renderSize: 1024,\n            samples: 4,\n            lodGenerationScale: 1,\n            lodGenerationOffset: -4,\n            renderTargetTextureType: Constants.TEXTURETYPE_HALF_FLOAT,\n            generateMipmaps: true,\n        };\n    }\n    /**\n     * constructor\n     * @param options Defines the options we want to customize the helper\n     * @param scene The scene to add the material to\n     */\n    constructor(options, scene) {\n        this._opaqueRenderTarget = null;\n        this._opaqueMeshesCache = [];\n        this._transparentMeshesCache = [];\n        this._materialObservers = {};\n        this._options = {\n            ...TransmissionHelper._GetDefaultOptions(),\n            ...options,\n        };\n        this._scene = scene;\n        this._scene._transmissionHelper = this;\n        this.onErrorObservable = new Observable();\n        this._scene.onDisposeObservable.addOnce(() => {\n            this.dispose();\n        });\n        this._parseScene();\n        this._setupRenderTargets();\n    }\n    /**\n     * Updates the background according to the new options\n     * @param options\n     */\n    updateOptions(options) {\n        // First check if any options are actually being changed. If not, exit.\n        const newValues = Object.keys(options).filter((key) => this._options[key] !== options[key]);\n        if (!newValues.length) {\n            return;\n        }\n        const newOptions = {\n            ...this._options,\n            ...options,\n        };\n        const oldOptions = this._options;\n        this._options = newOptions;\n        // If size changes, recreate everything\n        if (newOptions.renderSize !== oldOptions.renderSize ||\n            newOptions.renderTargetTextureType !== oldOptions.renderTargetTextureType ||\n            newOptions.generateMipmaps !== oldOptions.generateMipmaps ||\n            !this._opaqueRenderTarget) {\n            this._setupRenderTargets();\n        }\n        else {\n            this._opaqueRenderTarget.samples = newOptions.samples;\n            this._opaqueRenderTarget.lodGenerationScale = newOptions.lodGenerationScale;\n            this._opaqueRenderTarget.lodGenerationOffset = newOptions.lodGenerationOffset;\n        }\n    }\n    /**\n     * @returns the opaque render target texture or null if not available.\n     */\n    getOpaqueTarget() {\n        return this._opaqueRenderTarget;\n    }\n    _shouldRenderAsTransmission(material) {\n        if (!material) {\n            return false;\n        }\n        if (material instanceof PBRMaterial && material.subSurface.isRefractionEnabled) {\n            return true;\n        }\n        return false;\n    }\n    _addMesh(mesh) {\n        this._materialObservers[mesh.uniqueId] = mesh.onMaterialChangedObservable.add(this._onMeshMaterialChanged.bind(this));\n        // we need to defer the processing because _addMesh may be called as part as an instance mesh creation, in which case some\n        // internal properties are not setup yet, like _sourceMesh (needed when doing mesh.material below)\n        Tools.SetImmediate(() => {\n            if (this._shouldRenderAsTransmission(mesh.material)) {\n                mesh.material.refractionTexture = this._opaqueRenderTarget;\n                if (this._transparentMeshesCache.indexOf(mesh) === -1) {\n                    this._transparentMeshesCache.push(mesh);\n                }\n            }\n            else {\n                if (this._opaqueMeshesCache.indexOf(mesh) === -1) {\n                    this._opaqueMeshesCache.push(mesh);\n                }\n            }\n        });\n    }\n    _removeMesh(mesh) {\n        mesh.onMaterialChangedObservable.remove(this._materialObservers[mesh.uniqueId]);\n        delete this._materialObservers[mesh.uniqueId];\n        let idx = this._transparentMeshesCache.indexOf(mesh);\n        if (idx !== -1) {\n            this._transparentMeshesCache.splice(idx, 1);\n        }\n        idx = this._opaqueMeshesCache.indexOf(mesh);\n        if (idx !== -1) {\n            this._opaqueMeshesCache.splice(idx, 1);\n        }\n    }\n    _parseScene() {\n        this._scene.meshes.forEach(this._addMesh.bind(this));\n        // Listen for when a mesh is added to the scene and add it to our cache lists.\n        this._scene.onNewMeshAddedObservable.add(this._addMesh.bind(this));\n        // Listen for when a mesh is removed from to the scene and remove it from our cache lists.\n        this._scene.onMeshRemovedObservable.add(this._removeMesh.bind(this));\n    }\n    // When one of the meshes in the scene has its material changed, make sure that it's in the correct cache list.\n    _onMeshMaterialChanged(mesh) {\n        const transparentIdx = this._transparentMeshesCache.indexOf(mesh);\n        const opaqueIdx = this._opaqueMeshesCache.indexOf(mesh);\n        // If the material is transparent, make sure that it's added to the transparent list and removed from the opaque list\n        const useTransmission = this._shouldRenderAsTransmission(mesh.material);\n        if (useTransmission) {\n            if (mesh.material instanceof PBRMaterial) {\n                mesh.material.subSurface.refractionTexture = this._opaqueRenderTarget;\n            }\n            if (opaqueIdx !== -1) {\n                this._opaqueMeshesCache.splice(opaqueIdx, 1);\n                this._transparentMeshesCache.push(mesh);\n            }\n            else if (transparentIdx === -1) {\n                this._transparentMeshesCache.push(mesh);\n            }\n            // If the material is opaque, make sure that it's added to the opaque list and removed from the transparent list\n        }\n        else {\n            if (transparentIdx !== -1) {\n                this._transparentMeshesCache.splice(transparentIdx, 1);\n                this._opaqueMeshesCache.push(mesh);\n            }\n            else if (opaqueIdx === -1) {\n                this._opaqueMeshesCache.push(mesh);\n            }\n        }\n    }\n    /**\n     * @internal\n     * Check if the opaque render target has not been disposed and can still be used.\n     * @returns\n     */\n    _isRenderTargetValid() {\n        return this._opaqueRenderTarget?.getInternalTexture() !== null;\n    }\n    /**\n     * @internal\n     * Setup the render targets according to the specified options.\n     */\n    _setupRenderTargets() {\n        if (this._opaqueRenderTarget) {\n            this._opaqueRenderTarget.dispose();\n        }\n        this._opaqueRenderTarget = new RenderTargetTexture(\"opaqueSceneTexture\", this._options.renderSize, this._scene, this._options.generateMipmaps, undefined, this._options.renderTargetTextureType);\n        this._opaqueRenderTarget.ignoreCameraViewport = true;\n        this._opaqueRenderTarget.renderList = this._opaqueMeshesCache;\n        this._opaqueRenderTarget.clearColor = this._options.clearColor?.clone() ?? this._scene.clearColor.clone();\n        this._opaqueRenderTarget.gammaSpace = false;\n        this._opaqueRenderTarget.lodGenerationScale = this._options.lodGenerationScale;\n        this._opaqueRenderTarget.lodGenerationOffset = this._options.lodGenerationOffset;\n        this._opaqueRenderTarget.samples = this._options.samples;\n        this._opaqueRenderTarget.renderSprites = true;\n        this._opaqueRenderTarget.renderParticles = true;\n        this._opaqueRenderTarget.disableImageProcessing = true;\n        let saveSceneEnvIntensity;\n        this._opaqueRenderTarget.onBeforeBindObservable.add((opaqueRenderTarget) => {\n            saveSceneEnvIntensity = this._scene.environmentIntensity;\n            this._scene.environmentIntensity = 1.0;\n            if (!this._options.clearColor) {\n                this._scene.clearColor.toLinearSpaceToRef(opaqueRenderTarget.clearColor, this._scene.getEngine().useExactSrgbConversions);\n            }\n            else {\n                opaqueRenderTarget.clearColor.copyFrom(this._options.clearColor);\n            }\n        });\n        this._opaqueRenderTarget.onAfterUnbindObservable.add(() => {\n            this._scene.environmentIntensity = saveSceneEnvIntensity;\n        });\n        this._transparentMeshesCache.forEach((mesh) => {\n            if (this._shouldRenderAsTransmission(mesh.material)) {\n                mesh.material.refractionTexture = this._opaqueRenderTarget;\n            }\n        });\n    }\n    /**\n     * Dispose all the elements created by the Helper.\n     */\n    dispose() {\n        this._scene._transmissionHelper = undefined;\n        if (this._opaqueRenderTarget) {\n            this._opaqueRenderTarget.dispose();\n            this._opaqueRenderTarget = null;\n        }\n        this._transparentMeshesCache = [];\n        this._opaqueMeshesCache = [];\n    }\n}\nconst NAME = \"KHR_materials_transmission\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_transmission/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_transmission {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 175;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n        if (this.enabled) {\n            loader.parent.transparencyAsCoverage = true;\n        }\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadTransparentPropertiesAsync(extensionContext, material, babylonMaterial, extension));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadTransparentPropertiesAsync(context, material, babylonMaterial, extension) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const pbrMaterial = babylonMaterial;\n        // Enables \"refraction\" texture which represents transmitted light.\n        pbrMaterial.subSurface.isRefractionEnabled = true;\n        // Since this extension models thin-surface transmission only, we must make IOR = 1.0\n        pbrMaterial.subSurface.volumeIndexOfRefraction = 1.0;\n        // Albedo colour will tint transmission.\n        pbrMaterial.subSurface.useAlbedoToTintRefraction = true;\n        if (extension.transmissionFactor !== undefined) {\n            pbrMaterial.subSurface.refractionIntensity = extension.transmissionFactor;\n            const scene = pbrMaterial.getScene();\n            if (pbrMaterial.subSurface.refractionIntensity && !scene._transmissionHelper) {\n                new TransmissionHelper({}, pbrMaterial.getScene());\n            }\n            else if (pbrMaterial.subSurface.refractionIntensity && !scene._transmissionHelper?._isRenderTargetValid()) {\n                // If the render target is not valid, recreate it.\n                scene._transmissionHelper?._setupRenderTargets();\n            }\n        }\n        else {\n            pbrMaterial.subSurface.refractionIntensity = 0.0;\n            pbrMaterial.subSurface.isRefractionEnabled = false;\n            return Promise.resolve();\n        }\n        pbrMaterial.subSurface.minimumThickness = 0.0;\n        pbrMaterial.subSurface.maximumThickness = 0.0;\n        if (extension.transmissionTexture) {\n            extension.transmissionTexture.nonColorData = true;\n            return this._loader.loadTextureInfoAsync(`${context}/transmissionTexture`, extension.transmissionTexture, undefined).then((texture) => {\n                texture.name = `${babylonMaterial.name} (Transmission)`;\n                pbrMaterial.subSurface.refractionIntensityTexture = texture;\n                pbrMaterial.subSurface.useGltfStyleTextures = true;\n            });\n        }\n        else {\n            return Promise.resolve();\n        }\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_transmission(loader));\n//# sourceMappingURL=KHR_materials_transmission.js.map"], "names": ["TransmissionHelper", "Constants", "options", "scene", "Observable", "key", "newOptions", "oldOptions", "material", "PBRMaterial", "mesh", "Tools", "idx", "transparentIdx", "opaqueIdx", "RenderTargetTexture", "saveSceneEnvIntensity", "opaqueRenderTarget", "NAME", "KHR_materials_transmission", "loader", "context", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "pbrMaterial", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "kVAUA,MAAMA,CAAmB,CAKrB,OAAO,oBAAqB,CACxB,MAAO,CACH,WAAY,KACZ,QAAS,EACT,mBAAoB,EACpB,oBAAqB,GACrB,wBAAyBC,EAAU,uBACnC,gBAAiB,EAC7B,CACK,CAMD,YAAYC,EAASC,EAAO,CACxB,KAAK,oBAAsB,KAC3B,KAAK,mBAAqB,GAC1B,KAAK,wBAA0B,GAC/B,KAAK,mBAAqB,GAC1B,KAAK,SAAW,CACZ,GAAGH,EAAmB,mBAAoB,EAC1C,GAAGE,CACf,EACQ,KAAK,OAASC,EACd,KAAK,OAAO,oBAAsB,KAClC,KAAK,kBAAoB,IAAIC,EAC7B,KAAK,OAAO,oBAAoB,QAAQ,IAAM,CAC1C,KAAK,QAAO,CACxB,CAAS,EACD,KAAK,YAAW,EAChB,KAAK,oBAAmB,CAC3B,CAKD,cAAcF,EAAS,CAGnB,GAAI,CADc,OAAO,KAAKA,CAAO,EAAE,OAAQG,GAAQ,KAAK,SAASA,CAAG,IAAMH,EAAQG,CAAG,CAAC,EAC3E,OACX,OAEJ,MAAMC,EAAa,CACf,GAAG,KAAK,SACR,GAAGJ,CACf,EACcK,EAAa,KAAK,SACxB,KAAK,SAAWD,EAEZA,EAAW,aAAeC,EAAW,YACrCD,EAAW,0BAA4BC,EAAW,yBAClDD,EAAW,kBAAoBC,EAAW,iBAC1C,CAAC,KAAK,oBACN,KAAK,oBAAmB,GAGxB,KAAK,oBAAoB,QAAUD,EAAW,QAC9C,KAAK,oBAAoB,mBAAqBA,EAAW,mBACzD,KAAK,oBAAoB,oBAAsBA,EAAW,oBAEjE,CAID,iBAAkB,CACd,OAAO,KAAK,mBACf,CACD,4BAA4BE,EAAU,CAClC,OAAKA,EAGD,GAAAA,aAAoBC,GAAeD,EAAS,WAAW,qBAFhD,EAMd,CACD,SAASE,EAAM,CACX,KAAK,mBAAmBA,EAAK,QAAQ,EAAIA,EAAK,4BAA4B,IAAI,KAAK,uBAAuB,KAAK,IAAI,CAAC,EAGpHC,EAAM,aAAa,IAAM,CACjB,KAAK,4BAA4BD,EAAK,QAAQ,GAC9CA,EAAK,SAAS,kBAAoB,KAAK,oBACnC,KAAK,wBAAwB,QAAQA,CAAI,IAAM,IAC/C,KAAK,wBAAwB,KAAKA,CAAI,GAItC,KAAK,mBAAmB,QAAQA,CAAI,IAAM,IAC1C,KAAK,mBAAmB,KAAKA,CAAI,CAGrD,CAAS,CACJ,CACD,YAAYA,EAAM,CACdA,EAAK,4BAA4B,OAAO,KAAK,mBAAmBA,EAAK,QAAQ,CAAC,EAC9E,OAAO,KAAK,mBAAmBA,EAAK,QAAQ,EAC5C,IAAIE,EAAM,KAAK,wBAAwB,QAAQF,CAAI,EAC/CE,IAAQ,IACR,KAAK,wBAAwB,OAAOA,EAAK,CAAC,EAE9CA,EAAM,KAAK,mBAAmB,QAAQF,CAAI,EACtCE,IAAQ,IACR,KAAK,mBAAmB,OAAOA,EAAK,CAAC,CAE5C,CACD,aAAc,CACV,KAAK,OAAO,OAAO,QAAQ,KAAK,SAAS,KAAK,IAAI,CAAC,EAEnD,KAAK,OAAO,yBAAyB,IAAI,KAAK,SAAS,KAAK,IAAI,CAAC,EAEjE,KAAK,OAAO,wBAAwB,IAAI,KAAK,YAAY,KAAK,IAAI,CAAC,CACtE,CAED,uBAAuBF,EAAM,CACzB,MAAMG,EAAiB,KAAK,wBAAwB,QAAQH,CAAI,EAC1DI,EAAY,KAAK,mBAAmB,QAAQJ,CAAI,EAE9B,KAAK,4BAA4BA,EAAK,QAAQ,GAE9DA,EAAK,oBAAoBD,IACzBC,EAAK,SAAS,WAAW,kBAAoB,KAAK,qBAElDI,IAAc,IACd,KAAK,mBAAmB,OAAOA,EAAW,CAAC,EAC3C,KAAK,wBAAwB,KAAKJ,CAAI,GAEjCG,IAAmB,IACxB,KAAK,wBAAwB,KAAKH,CAAI,GAKtCG,IAAmB,IACnB,KAAK,wBAAwB,OAAOA,EAAgB,CAAC,EACrD,KAAK,mBAAmB,KAAKH,CAAI,GAE5BI,IAAc,IACnB,KAAK,mBAAmB,KAAKJ,CAAI,CAG5C,CAMD,sBAAuB,CACnB,OAAO,KAAK,qBAAqB,mBAAkB,IAAO,IAC7D,CAKD,qBAAsB,CACd,KAAK,qBACL,KAAK,oBAAoB,UAE7B,KAAK,oBAAsB,IAAIK,EAAoB,qBAAsB,KAAK,SAAS,WAAY,KAAK,OAAQ,KAAK,SAAS,gBAAiB,OAAW,KAAK,SAAS,uBAAuB,EAC/L,KAAK,oBAAoB,qBAAuB,GAChD,KAAK,oBAAoB,WAAa,KAAK,mBAC3C,KAAK,oBAAoB,WAAa,KAAK,SAAS,YAAY,MAAK,GAAM,KAAK,OAAO,WAAW,MAAK,EACvG,KAAK,oBAAoB,WAAa,GACtC,KAAK,oBAAoB,mBAAqB,KAAK,SAAS,mBAC5D,KAAK,oBAAoB,oBAAsB,KAAK,SAAS,oBAC7D,KAAK,oBAAoB,QAAU,KAAK,SAAS,QACjD,KAAK,oBAAoB,cAAgB,GACzC,KAAK,oBAAoB,gBAAkB,GAC3C,KAAK,oBAAoB,uBAAyB,GAClD,IAAIC,EACJ,KAAK,oBAAoB,uBAAuB,IAAKC,GAAuB,CACxED,EAAwB,KAAK,OAAO,qBACpC,KAAK,OAAO,qBAAuB,EAC9B,KAAK,SAAS,WAIfC,EAAmB,WAAW,SAAS,KAAK,SAAS,UAAU,EAH/D,KAAK,OAAO,WAAW,mBAAmBA,EAAmB,WAAY,KAAK,OAAO,UAAW,EAAC,uBAAuB,CAKxI,CAAS,EACD,KAAK,oBAAoB,wBAAwB,IAAI,IAAM,CACvD,KAAK,OAAO,qBAAuBD,CAC/C,CAAS,EACD,KAAK,wBAAwB,QAASN,GAAS,CACvC,KAAK,4BAA4BA,EAAK,QAAQ,IAC9CA,EAAK,SAAS,kBAAoB,KAAK,oBAEvD,CAAS,CACJ,CAID,SAAU,CACN,KAAK,OAAO,oBAAsB,OAC9B,KAAK,sBACL,KAAK,oBAAoB,UACzB,KAAK,oBAAsB,MAE/B,KAAK,wBAA0B,GAC/B,KAAK,mBAAqB,EAC7B,CACL,CACA,MAAMQ,EAAO,6BAKN,MAAMC,CAA2B,CAIpC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,EAC5C,KAAK,UACLE,EAAO,OAAO,uBAAyB,GAE9C,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BC,EAASb,EAAUc,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBF,EAASb,EAAU,KAAK,KAAM,CAACgB,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BL,EAASb,EAAUc,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,gCAAgCF,EAAkBhB,EAAUc,EAAiBG,CAAS,CAAC,EACnG,QAAQ,IAAIC,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,gCAAgCL,EAASb,EAAUc,EAAiBG,EAAW,CAC3E,GAAI,EAAEH,aAA2Bb,GAC7B,MAAM,IAAI,MAAM,GAAGY,CAAO,+BAA+B,EAE7D,MAAMM,EAAcL,EAOpB,GALAK,EAAY,WAAW,oBAAsB,GAE7CA,EAAY,WAAW,wBAA0B,EAEjDA,EAAY,WAAW,0BAA4B,GAC/CF,EAAU,qBAAuB,OAAW,CAC5CE,EAAY,WAAW,oBAAsBF,EAAU,mBACvD,MAAMtB,EAAQwB,EAAY,WACtBA,EAAY,WAAW,qBAAuB,CAACxB,EAAM,oBACrD,IAAIH,EAAmB,CAAE,EAAE2B,EAAY,SAAU,CAAA,EAE5CA,EAAY,WAAW,qBAAuB,CAACxB,EAAM,qBAAqB,wBAE/EA,EAAM,qBAAqB,qBAElC,KAEG,QAAAwB,EAAY,WAAW,oBAAsB,EAC7CA,EAAY,WAAW,oBAAsB,GACtC,QAAQ,UAInB,OAFAA,EAAY,WAAW,iBAAmB,EAC1CA,EAAY,WAAW,iBAAmB,EACtCF,EAAU,qBACVA,EAAU,oBAAoB,aAAe,GACtC,KAAK,QAAQ,qBAAqB,GAAGJ,CAAO,uBAAwBI,EAAU,oBAAqB,MAAS,EAAE,KAAMG,GAAY,CACnIA,EAAQ,KAAO,GAAGN,EAAgB,IAAI,kBACtCK,EAAY,WAAW,2BAA6BC,EACpDD,EAAY,WAAW,qBAAuB,EAC9D,CAAa,GAGM,QAAQ,SAEtB,CACL,CACAE,EAAwBX,CAAI,EAC5BY,EAAsBZ,EAAM,GAAOE,GAAW,IAAID,EAA2BC,CAAM,CAAC", "x_google_ignoreList": [0]}