{".class": "MypyFile", "_fullname": "extensions.visualization.funnel_plot", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BasePlot": {".class": "SymbolTableNode", "cross_ref": "extensions.visualization.base_plot.BasePlot", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FunnelPlot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["extensions.visualization.base_plot.BasePlot"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "extensions.visualization.funnel_plot.FunnelPlot", "name": "FunnelPlot", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "extensions.visualization.funnel_plot.FunnelPlot", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "extensions.visualization.funnel_plot", "mro": ["extensions.visualization.funnel_plot.FunnelPlot", "extensions.visualization.base_plot.BasePlot", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_add_studies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fig", "studies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.funnel_plot.FunnelPlot._add_studies", "name": "_add_studies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fig", "studies"], "arg_types": ["extensions.visualization.funnel_plot.FunnelPlot", {".class": "AnyType", "missing_import_name": "extensions.visualization.funnel_plot.go", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_studies of FunnelPlot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_symmetry_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fig", "studies", "summary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.funnel_plot.FunnelPlot._add_symmetry_lines", "name": "_add_symmetry_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fig", "studies", "summary"], "arg_types": ["extensions.visualization.funnel_plot.FunnelPlot", {".class": "AnyType", "missing_import_name": "extensions.visualization.funnel_plot.go", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_symmetry_lines of FunnelPlot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_empty_plot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.funnel_plot.FunnelPlot._create_empty_plot", "name": "_create_empty_plot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["extensions.visualization.funnel_plot.FunnelPlot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_empty_plot of FunnelPlot", "ret_type": {".class": "AnyType", "missing_import_name": "extensions.visualization.funnel_plot.go", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_figure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.funnel_plot.FunnelPlot._create_figure", "name": "_create_figure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["extensions.visualization.funnel_plot.FunnelPlot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_figure of FunnelPlot", "ret_type": {".class": "AnyType", "missing_import_name": "extensions.visualization.funnel_plot.go", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.funnel_plot.FunnelPlot._update_layout", "name": "_update_layout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fig"], "arg_types": ["extensions.visualization.funnel_plot.FunnelPlot", {".class": "AnyType", "missing_import_name": "extensions.visualization.funnel_plot.go", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_layout of FunnelPlot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "extensions.visualization.funnel_plot.FunnelPlot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "extensions.visualization.funnel_plot.FunnelPlot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.funnel_plot.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.funnel_plot.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.funnel_plot.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.funnel_plot.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.funnel_plot.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.funnel_plot.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "go": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "extensions.visualization.funnel_plot.go", "name": "go", "type": {".class": "AnyType", "missing_import_name": "extensions.visualization.funnel_plot.go", "source_any": null, "type_of_any": 3}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}}, "path": "E:\\xzyxgg\\extensions\\visualization\\funnel_plot.py"}