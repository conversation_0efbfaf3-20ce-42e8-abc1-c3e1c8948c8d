{"version": 3, "file": "KHR_materials_pbrSpecularGlossiness-B2XZLso8.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_pbrSpecularGlossiness.js"], "sourcesContent": ["import { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_pbrSpecularGlossiness\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Archived/KHR_materials_pbrSpecularGlossiness/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_pbrSpecularGlossiness {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 200;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialBasePropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadSpecularGlossinessPropertiesAsync(extensionContext, extension, babylonMaterial));\n            this._loader.loadMaterialAlphaProperties(context, material, babylonMaterial);\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadSpecularGlossinessPropertiesAsync(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        babylonMaterial.metallic = null;\n        babylonMaterial.roughness = null;\n        if (properties.diffuseFactor) {\n            babylonMaterial.albedoColor = Color3.FromArray(properties.diffuseFactor);\n            babylonMaterial.alpha = properties.diffuseFactor[3];\n        }\n        else {\n            babylonMaterial.albedoColor = Color3.White();\n        }\n        babylonMaterial.reflectivityColor = properties.specularFactor ? Color3.FromArray(properties.specularFactor) : Color3.White();\n        babylonMaterial.microSurface = properties.glossinessFactor == undefined ? 1 : properties.glossinessFactor;\n        if (properties.diffuseTexture) {\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/diffuseTexture`, properties.diffuseTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Diffuse)`;\n                babylonMaterial.albedoTexture = texture;\n            }));\n        }\n        if (properties.specularGlossinessTexture) {\n            promises.push(this._loader.loadTextureInfoAsync(`${context}/specularGlossinessTexture`, properties.specularGlossinessTexture, (texture) => {\n                texture.name = `${babylonMaterial.name} (Specular Glossiness)`;\n                babylonMaterial.reflectivityTexture = texture;\n                babylonMaterial.reflectivityTexture.hasAlpha = true;\n            }));\n            babylonMaterial.useMicroSurfaceFromReflectivityMapAlpha = true;\n        }\n        return Promise.all(promises).then(() => { });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_pbrSpecularGlossiness(loader));\n//# sourceMappingURL=KHR_materials_pbrSpecularGlossiness.js.map"], "names": ["NAME", "KHR_materials_pbrSpecularGlossiness", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "properties", "PBRMaterial", "Color3", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "uTAIA,MAAMA,EAAO,sCAKN,MAAMC,CAAoC,CAI7C,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,gCAAgCN,EAASC,EAAUC,CAAe,CAAC,EAC9FI,EAAS,KAAK,KAAK,uCAAuCF,EAAkBC,EAAWH,CAAe,CAAC,EACvG,KAAK,QAAQ,4BAA4BF,EAASC,EAAUC,CAAe,EACpE,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,uCAAuCN,EAASO,EAAYL,EAAiB,CACzE,GAAI,EAAEA,aAA2BM,GAC7B,MAAM,IAAI,MAAM,GAAGR,CAAO,+BAA+B,EAE7D,MAAMM,EAAW,IAAI,MACrB,OAAAJ,EAAgB,SAAW,KAC3BA,EAAgB,UAAY,KACxBK,EAAW,eACXL,EAAgB,YAAcO,EAAO,UAAUF,EAAW,aAAa,EACvEL,EAAgB,MAAQK,EAAW,cAAc,CAAC,GAGlDL,EAAgB,YAAcO,EAAO,QAEzCP,EAAgB,kBAAoBK,EAAW,eAAiBE,EAAO,UAAUF,EAAW,cAAc,EAAIE,EAAO,MAAK,EAC1HP,EAAgB,aAAeK,EAAW,kBAAoB,KAAY,EAAIA,EAAW,iBACrFA,EAAW,gBACXD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,kBAAmBO,EAAW,eAAiBG,GAAY,CACjHA,EAAQ,KAAO,GAAGR,EAAgB,IAAI,aACtCA,EAAgB,cAAgBQ,CACnC,CAAA,CAAC,EAEFH,EAAW,4BACXD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGN,CAAO,6BAA8BO,EAAW,0BAA4BG,GAAY,CACvIA,EAAQ,KAAO,GAAGR,EAAgB,IAAI,yBACtCA,EAAgB,oBAAsBQ,EACtCR,EAAgB,oBAAoB,SAAW,EAClD,CAAA,CAAC,EACFA,EAAgB,wCAA0C,IAEvD,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CAC9C,CACL,CACAK,EAAwBd,CAAI,EAC5Be,EAAsBf,EAAM,GAAOE,GAAW,IAAID,EAAoCC,CAAM,CAAC", "x_google_ignoreList": [0]}