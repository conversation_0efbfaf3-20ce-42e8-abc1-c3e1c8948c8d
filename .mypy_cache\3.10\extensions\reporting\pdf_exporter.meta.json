{"data_mtime": 1751179720, "dep_lines": [9, 5, 6, 7, 8, 348, 1, 1, 1, 1, 12, 346, 345, 11, 306], "dep_prios": [5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 5, 20, 20, 5, 20], "dependencies": ["urllib.parse", "os", "logging", "pathlib", "typing", "io", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "3adb914a53139b5fe09befc092aeac51a4fe6c84", "id": "extensions.reporting.pdf_exporter", "ignore_all": true, "interface_hash": "e1ac281cddbacc2309e26cd8866f44da01512696", "mtime": 1751085617, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\extensions\\reporting\\pdf_exporter.py", "plugin_data": null, "size": 12156, "suppressed": ["weasyprint.text.fonts", "reportlab.lib.colors", "reportlab.pdfgen", "weasyprint", "PyPDF2"], "version_id": "1.15.0"}