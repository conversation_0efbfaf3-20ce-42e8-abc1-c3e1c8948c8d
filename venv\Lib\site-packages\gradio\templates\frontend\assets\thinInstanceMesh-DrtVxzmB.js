import{ab as r,h as g,M as c,aa as s,ac as S,ad as _,a as u,V as B}from"./index-SLPGw9aX.js";r.prototype.thinInstanceAdd=function(t,e=!0){if(!this.getScene().getEngine().getCaps().instancedArrays)return g.<PERSON>rror("Thin Instances are not supported on this device as Instanced Array extension not supported"),-1;this._thinInstanceUpdateBufferSize("matrix",Array.isArray(t)?t.length:1);const a=this._thinInstanceDataStorage.instancesCount;if(Array.isArray(t))for(let n=0;n<t.length;++n)this.thinInstanceSetMatrixAt(this._thinInstanceDataStorage.instancesCount++,t[n],n===t.length-1&&e);else this.thinInstanceSetMatrixAt(this._thinInstanceDataStorage.instancesCount++,t,e);return a};r.prototype.thinInstanceAddSelf=function(t=!0){return this.thinInstanceAdd(c.IdentityReadOnly,t)};r.prototype.thinInstanceRegisterAttribute=function(t,e){t===s.ColorKind&&(t=s.ColorInstanceKind),this.removeVerticesData(t),this._thinInstanceInitializeUserStorage(),this._userThinInstanceBuffersStorage.strides[t]=e,this._userThinInstanceBuffersStorage.sizes[t]=e*Math.max(32,this._thinInstanceDataStorage.instancesCount),this._userThinInstanceBuffersStorage.data[t]=new Float32Array(this._userThinInstanceBuffersStorage.sizes[t]),this._userThinInstanceBuffersStorage.vertexBuffers[t]=new s(this.getEngine(),this._userThinInstanceBuffersStorage.data[t],t,!0,!1,e,!0),this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[t])};r.prototype.thinInstanceSetMatrixAt=function(t,e,a=!0){if(!this._thinInstanceDataStorage.matrixData||t>=this._thinInstanceDataStorage.instancesCount)return!1;const n=this._thinInstanceDataStorage.matrixData;return e.copyToArray(n,t*16),this._thinInstanceDataStorage.worldMatrices&&(this._thinInstanceDataStorage.worldMatrices[t]=e),a&&(this.thinInstanceBufferUpdated("matrix"),this.doNotSyncBoundingInfo||this.thinInstanceRefreshBoundingInfo(!1)),!0};r.prototype.thinInstanceSetAttributeAt=function(t,e,a,n=!0){return t===s.ColorKind&&(t=s.ColorInstanceKind),!this._userThinInstanceBuffersStorage||!this._userThinInstanceBuffersStorage.data[t]||e>=this._thinInstanceDataStorage.instancesCount?!1:(this._thinInstanceUpdateBufferSize(t,0),this._userThinInstanceBuffersStorage.data[t].set(a,e*this._userThinInstanceBuffersStorage.strides[t]),n&&this.thinInstanceBufferUpdated(t),!0)};Object.defineProperty(r.prototype,"thinInstanceCount",{get:function(){return this._thinInstanceDataStorage.instancesCount},set:function(t){const e=this._thinInstanceDataStorage.matrixData??this.source?._thinInstanceDataStorage.matrixData,a=e?e.length/16:0;t<=a&&(this._thinInstanceDataStorage.instancesCount=t)},enumerable:!0,configurable:!0});r.prototype._thinInstanceCreateMatrixBuffer=function(t,e,a=!0){const n=new S(this.getEngine(),e,!a,16,!1,!0);for(let h=0;h<4;h++)this.setVerticesBuffer(n.createVertexBuffer(t+h,h*4,4));return n};r.prototype.thinInstanceSetBuffer=function(t,e,a=0,n=!0){a=a||16,t==="matrix"?(this._thinInstanceDataStorage.matrixBuffer?.dispose(),this._thinInstanceDataStorage.matrixBuffer=null,this._thinInstanceDataStorage.matrixBufferSize=e?e.length:32*a,this._thinInstanceDataStorage.matrixData=e,this._thinInstanceDataStorage.worldMatrices=null,e!==null?(this._thinInstanceDataStorage.instancesCount=e.length/a,this._thinInstanceDataStorage.matrixBuffer=this._thinInstanceCreateMatrixBuffer("world",e,n),this.doNotSyncBoundingInfo||this.thinInstanceRefreshBoundingInfo(!1)):(this._thinInstanceDataStorage.instancesCount=0,this.doNotSyncBoundingInfo||this.refreshBoundingInfo())):t==="previousMatrix"?(this._thinInstanceDataStorage.previousMatrixBuffer?.dispose(),this._thinInstanceDataStorage.previousMatrixBuffer=null,this._thinInstanceDataStorage.previousMatrixData=e,e!==null&&(this._thinInstanceDataStorage.previousMatrixBuffer=this._thinInstanceCreateMatrixBuffer("previousWorld",e,n))):(t===s.ColorKind&&(t=s.ColorInstanceKind),e===null?this._userThinInstanceBuffersStorage?.data[t]&&(this.removeVerticesData(t),delete this._userThinInstanceBuffersStorage.data[t],delete this._userThinInstanceBuffersStorage.strides[t],delete this._userThinInstanceBuffersStorage.sizes[t],delete this._userThinInstanceBuffersStorage.vertexBuffers[t]):(this._thinInstanceInitializeUserStorage(),this._userThinInstanceBuffersStorage.data[t]=e,this._userThinInstanceBuffersStorage.strides[t]=a,this._userThinInstanceBuffersStorage.sizes[t]=e.length,this._userThinInstanceBuffersStorage.vertexBuffers[t]=new s(this.getEngine(),e,t,!n,!1,a,!0),this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[t])))};r.prototype.thinInstanceBufferUpdated=function(t){t==="matrix"?(this.thinInstanceAllowAutomaticStaticBufferRecreation&&this._thinInstanceDataStorage.matrixBuffer&&!this._thinInstanceDataStorage.matrixBuffer.isUpdatable()&&this._thinInstanceRecreateBuffer(t),this._thinInstanceDataStorage.matrixBuffer?.updateDirectly(this._thinInstanceDataStorage.matrixData,0,this._thinInstanceDataStorage.instancesCount)):t==="previousMatrix"?(this.thinInstanceAllowAutomaticStaticBufferRecreation&&this._thinInstanceDataStorage.previousMatrixBuffer&&!this._thinInstanceDataStorage.previousMatrixBuffer.isUpdatable()&&this._thinInstanceRecreateBuffer(t),this._thinInstanceDataStorage.previousMatrixBuffer?.updateDirectly(this._thinInstanceDataStorage.previousMatrixData,0,this._thinInstanceDataStorage.instancesCount)):(t===s.ColorKind&&(t=s.ColorInstanceKind),this._userThinInstanceBuffersStorage?.vertexBuffers[t]&&(this.thinInstanceAllowAutomaticStaticBufferRecreation&&!this._userThinInstanceBuffersStorage.vertexBuffers[t].isUpdatable()&&this._thinInstanceRecreateBuffer(t),this._userThinInstanceBuffersStorage.vertexBuffers[t].updateDirectly(this._userThinInstanceBuffersStorage.data[t],0)))};r.prototype.thinInstancePartialBufferUpdate=function(t,e,a){t==="matrix"?this._thinInstanceDataStorage.matrixBuffer&&this._thinInstanceDataStorage.matrixBuffer.updateDirectly(e,a):(t===s.ColorKind&&(t=s.ColorInstanceKind),this._userThinInstanceBuffersStorage?.vertexBuffers[t]&&this._userThinInstanceBuffersStorage.vertexBuffers[t].updateDirectly(e,a))};r.prototype.thinInstanceGetWorldMatrices=function(){if(!this._thinInstanceDataStorage.matrixData||!this._thinInstanceDataStorage.matrixBuffer)return[];const t=this._thinInstanceDataStorage.matrixData;if(!this._thinInstanceDataStorage.worldMatrices){this._thinInstanceDataStorage.worldMatrices=[];for(let e=0;e<this._thinInstanceDataStorage.instancesCount;++e)this._thinInstanceDataStorage.worldMatrices[e]=c.FromArray(t,e*16)}return this._thinInstanceDataStorage.worldMatrices};r.prototype.thinInstanceRefreshBoundingInfo=function(t=!1,e=!1,a=!1){if(!this._thinInstanceDataStorage.matrixData||!this._thinInstanceDataStorage.matrixBuffer)return;const n=this._thinInstanceDataStorage.boundingVectors;if(t||!this.rawBoundingInfo){n.length=0,this.refreshBoundingInfo(e,a);const o=this.getBoundingInfo();this.rawBoundingInfo=new _(o.minimum,o.maximum)}const h=this.getBoundingInfo(),i=this._thinInstanceDataStorage.matrixData;if(n.length===0)for(let o=0;o<h.boundingBox.vectors.length;++o)n.push(h.boundingBox.vectors[o].clone());u.Vector3[0].setAll(Number.POSITIVE_INFINITY),u.Vector3[1].setAll(Number.NEGATIVE_INFINITY);for(let o=0;o<this._thinInstanceDataStorage.instancesCount;++o){c.FromArrayToRef(i,o*16,u.Matrix[0]);for(let f=0;f<n.length;++f)B.TransformCoordinatesToRef(n[f],u.Matrix[0],u.Vector3[2]),u.Vector3[0].minimizeInPlace(u.Vector3[2]),u.Vector3[1].maximizeInPlace(u.Vector3[2])}h.reConstruct(u.Vector3[0],u.Vector3[1]),this._updateBoundingInfo()};r.prototype._thinInstanceRecreateBuffer=function(t,e=!0){t==="matrix"?(this._thinInstanceDataStorage.matrixBuffer?.dispose(),this._thinInstanceDataStorage.matrixBuffer=this._thinInstanceCreateMatrixBuffer("world",this._thinInstanceDataStorage.matrixData,e)):t==="previousMatrix"?this._scene.needsPreviousWorldMatrices&&(this._thinInstanceDataStorage.previousMatrixBuffer?.dispose(),this._thinInstanceDataStorage.previousMatrixBuffer=this._thinInstanceCreateMatrixBuffer("previousWorld",this._thinInstanceDataStorage.previousMatrixData??this._thinInstanceDataStorage.matrixData,e)):(t===s.ColorKind&&(t=s.ColorInstanceKind),this._userThinInstanceBuffersStorage.vertexBuffers[t]?.dispose(),this._userThinInstanceBuffersStorage.vertexBuffers[t]=new s(this.getEngine(),this._userThinInstanceBuffersStorage.data[t],t,!e,!1,this._userThinInstanceBuffersStorage.strides[t],!0),this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[t]))};r.prototype._thinInstanceUpdateBufferSize=function(t,e=1){t===s.ColorKind&&(t=s.ColorInstanceKind);const a=t==="matrix";if(!a&&(!this._userThinInstanceBuffersStorage||!this._userThinInstanceBuffersStorage.strides[t]))return;const n=a?16:this._userThinInstanceBuffersStorage.strides[t],h=a?this._thinInstanceDataStorage.matrixBufferSize:this._userThinInstanceBuffersStorage.sizes[t];let i=a?this._thinInstanceDataStorage.matrixData:this._userThinInstanceBuffersStorage.data[t];const o=(this._thinInstanceDataStorage.instancesCount+e)*n;let f=h;for(;f<o;)f*=2;if(!i||h!=f){if(!i)i=new Float32Array(f);else{const I=new Float32Array(f);I.set(i,0),i=I}a?(this._thinInstanceDataStorage.matrixBuffer?.dispose(),this._thinInstanceDataStorage.matrixBuffer=this._thinInstanceCreateMatrixBuffer("world",i,!1),this._thinInstanceDataStorage.matrixData=i,this._thinInstanceDataStorage.matrixBufferSize=f,this._scene.needsPreviousWorldMatrices&&!this._thinInstanceDataStorage.previousMatrixData&&(this._thinInstanceDataStorage.previousMatrixBuffer?.dispose(),this._thinInstanceDataStorage.previousMatrixBuffer=this._thinInstanceCreateMatrixBuffer("previousWorld",i,!1))):(this._userThinInstanceBuffersStorage.vertexBuffers[t]?.dispose(),this._userThinInstanceBuffersStorage.data[t]=i,this._userThinInstanceBuffersStorage.sizes[t]=f,this._userThinInstanceBuffersStorage.vertexBuffers[t]=new s(this.getEngine(),i,t,!0,!1,n,!0),this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[t]))}};r.prototype._thinInstanceInitializeUserStorage=function(){this._userThinInstanceBuffersStorage||(this._userThinInstanceBuffersStorage={data:{},sizes:{},vertexBuffers:{},strides:{}})};r.prototype._disposeThinInstanceSpecificData=function(){this._thinInstanceDataStorage?.matrixBuffer&&(this._thinInstanceDataStorage.matrixBuffer.dispose(),this._thinInstanceDataStorage.matrixBuffer=null)};
//# sourceMappingURL=thinInstanceMesh-DrtVxzmB.js.map
