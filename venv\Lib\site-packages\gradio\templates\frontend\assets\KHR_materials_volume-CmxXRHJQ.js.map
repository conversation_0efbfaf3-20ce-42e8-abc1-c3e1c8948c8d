{"version": 3, "file": "KHR_materials_volume-CmxXRHJQ.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_volume.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_volume\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_volume/README.md)\n * @since 5.0.0\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_volume {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 173;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n        if (this.enabled) {\n            // We need to disable instance usage because the attenuation factor depends on the node scale of each individual mesh\n            this._loader._disableInstancedMesh++;\n        }\n    }\n    /** @internal */\n    dispose() {\n        if (this.enabled) {\n            this._loader._disableInstancedMesh--;\n        }\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadVolumePropertiesAsync(extensionContext, material, babylonMaterial, extension));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadVolumePropertiesAsync(context, material, babylonMaterial, extension) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        // If transparency isn't enabled already, this extension shouldn't do anything.\n        // i.e. it requires either the KHR_materials_transmission or KHR_materials_diffuse_transmission extensions.\n        if ((!babylonMaterial.subSurface.isRefractionEnabled && !babylonMaterial.subSurface.isTranslucencyEnabled) || !extension.thicknessFactor) {\n            return Promise.resolve();\n        }\n        // IOR in this extension only affects interior.\n        babylonMaterial.subSurface.volumeIndexOfRefraction = babylonMaterial.indexOfRefraction;\n        const attenuationDistance = extension.attenuationDistance !== undefined ? extension.attenuationDistance : Number.MAX_VALUE;\n        babylonMaterial.subSurface.tintColorAtDistance = attenuationDistance;\n        if (extension.attenuationColor !== undefined && extension.attenuationColor.length == 3) {\n            babylonMaterial.subSurface.tintColor.copyFromFloats(extension.attenuationColor[0], extension.attenuationColor[1], extension.attenuationColor[2]);\n        }\n        babylonMaterial.subSurface.minimumThickness = 0.0;\n        babylonMaterial.subSurface.maximumThickness = extension.thicknessFactor;\n        babylonMaterial.subSurface.useThicknessAsDepth = true;\n        if (extension.thicknessTexture) {\n            extension.thicknessTexture.nonColorData = true;\n            return this._loader.loadTextureInfoAsync(`${context}/thicknessTexture`, extension.thicknessTexture).then((texture) => {\n                texture.name = `${babylonMaterial.name} (Thickness)`;\n                babylonMaterial.subSurface.thicknessTexture = texture;\n                babylonMaterial.subSurface.useGltfStyleTextures = true;\n            });\n        }\n        else {\n            return Promise.resolve();\n        }\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_volume(loader));\n//# sourceMappingURL=KHR_materials_volume.js.map"], "names": ["NAME", "KHR_materials_volume", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "PBRMaterial", "attenuationDistance", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "gTAGA,MAAMA,EAAO,uBAMN,MAAMC,CAAqB,CAI9B,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,EAC5C,KAAK,SAEL,KAAK,QAAQ,uBAEpB,CAED,SAAU,CACF,KAAK,SACL,KAAK,QAAQ,wBAEjB,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,2BAA2BF,EAAkBH,EAAUC,EAAiBG,CAAS,CAAC,EAC9F,QAAQ,IAAIC,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,2BAA2BN,EAASC,EAAUC,EAAiBG,EAAW,CACtE,GAAI,EAAEH,aAA2BK,GAC7B,MAAM,IAAI,MAAM,GAAGP,CAAO,+BAA+B,EAI7D,GAAK,CAACE,EAAgB,WAAW,qBAAuB,CAACA,EAAgB,WAAW,uBAA0B,CAACG,EAAU,gBACrH,OAAO,QAAQ,UAGnBH,EAAgB,WAAW,wBAA0BA,EAAgB,kBACrE,MAAMM,EAAsBH,EAAU,sBAAwB,OAAYA,EAAU,oBAAsB,OAAO,UAQjH,OAPAH,EAAgB,WAAW,oBAAsBM,EAC7CH,EAAU,mBAAqB,QAAaA,EAAU,iBAAiB,QAAU,GACjFH,EAAgB,WAAW,UAAU,eAAeG,EAAU,iBAAiB,CAAC,EAAGA,EAAU,iBAAiB,CAAC,EAAGA,EAAU,iBAAiB,CAAC,CAAC,EAEnJH,EAAgB,WAAW,iBAAmB,EAC9CA,EAAgB,WAAW,iBAAmBG,EAAU,gBACxDH,EAAgB,WAAW,oBAAsB,GAC7CG,EAAU,kBACVA,EAAU,iBAAiB,aAAe,GACnC,KAAK,QAAQ,qBAAqB,GAAGL,CAAO,oBAAqBK,EAAU,gBAAgB,EAAE,KAAMI,GAAY,CAClHA,EAAQ,KAAO,GAAGP,EAAgB,IAAI,eACtCA,EAAgB,WAAW,iBAAmBO,EAC9CP,EAAgB,WAAW,qBAAuB,EAClE,CAAa,GAGM,QAAQ,SAEtB,CACL,CACAQ,EAAwBb,CAAI,EAC5Bc,EAAsBd,EAAM,GAAOE,GAAW,IAAID,EAAqBC,CAAM,CAAC", "x_google_ignoreList": [0]}