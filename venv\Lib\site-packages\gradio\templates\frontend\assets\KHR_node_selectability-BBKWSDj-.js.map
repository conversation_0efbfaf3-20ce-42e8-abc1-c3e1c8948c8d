{"version": 3, "file": "KHR_node_selectability-BBKWSDj-.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_node_selectability.js"], "sourcesContent": ["import { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport { addNewInteractivityFlowGraphMapping } from \"./KHR_interactivity/declarationMapper.js\";\nimport { AddObjectAccessorToKey } from \"./objectModelMapping.js\";\nconst NAME = \"KHR_node_selectability\";\n// add the interactivity mapping for the onSelect event\naddNewInteractivityFlowGraphMapping(\"event/onSelect\", NAME, {\n    // using GetVariable as the nodeIndex is a configuration and not a value (i.e. it's not mutable)\n    blocks: [\"FlowGraphMeshPickEventBlock\" /* FlowGraphBlockNames.MeshPickEvent */, \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */, \"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */, \"KHR_interactivity/FlowGraphGLTFDataProvider\"],\n    configuration: {\n        stopPropagation: { name: \"stopPropagation\" },\n        nodeIndex: {\n            name: \"variable\",\n            toBlock: \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */,\n            dataTransformer(data) {\n                return [\"pickedMesh_\" + data[0]];\n            },\n        },\n    },\n    outputs: {\n        values: {\n            selectedNodeIndex: { name: \"index\", toBlock: \"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */ },\n            controllerIndex: { name: \"pointerId\" },\n            selectionPoint: { name: \"pickedPoint\" },\n            selectionRayOrigin: { name: \"pickOrigin\" },\n        },\n        flows: {\n            out: { name: \"done\" },\n        },\n    },\n    interBlockConnectors: [\n        {\n            input: \"asset\",\n            output: \"value\",\n            inputBlockIndex: 0,\n            outputBlockIndex: 1,\n            isVariable: true,\n        },\n        {\n            input: \"array\",\n            output: \"nodes\",\n            inputBlockIndex: 2,\n            outputBlockIndex: 3,\n            isVariable: true,\n        },\n        {\n            input: \"object\",\n            output: \"pickedMesh\",\n            inputBlockIndex: 2,\n            outputBlockIndex: 0,\n            isVariable: true,\n        },\n    ],\n    extraProcessor(gltfBlock, _declaration, _mapping, _arrays, serializedObjects, context, globalGLTF) {\n        // add the glTF to the configuration of the last serialized object\n        const serializedObject = serializedObjects[serializedObjects.length - 1];\n        serializedObject.config = serializedObject.config || {};\n        serializedObject.config.glTF = globalGLTF;\n        // find the listener nodeIndex value\n        const nodeIndex = gltfBlock.configuration?.[\"nodeIndex\"]?.value[0];\n        if (nodeIndex === undefined || typeof nodeIndex !== \"number\") {\n            throw new Error(\"nodeIndex not found in configuration\");\n        }\n        const variableName = \"pickedMesh_\" + nodeIndex;\n        // find the nodeIndex value\n        serializedObjects[1].config.variable = variableName;\n        context._userVariables[variableName] = {\n            className: \"Mesh\",\n            id: globalGLTF?.nodes?.[nodeIndex]._babylonTransformNode?.id,\n            uniqueId: globalGLTF?.nodes?.[nodeIndex]._babylonTransformNode?.uniqueId,\n        };\n        return serializedObjects;\n    },\n});\n// object model extension for selectable\nAddObjectAccessorToKey(\"/nodes/{}/extensions/KHR_node_selectability/selectable\", {\n    get: (node) => {\n        const tn = node._babylonTransformNode;\n        if (tn && tn.isPickable !== undefined) {\n            return tn.isPickable;\n        }\n        return true;\n    },\n    set: (value, node) => {\n        node._primitiveBabylonMeshes?.forEach((mesh) => {\n            mesh.isPickable = value;\n        });\n    },\n    getTarget: (node) => node._babylonTransformNode,\n    getPropertyName: [() => \"isPickable\"],\n    type: \"boolean\",\n});\n/**\n * Loader extension for KHR_selectability\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_node_selectability {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = loader.isExtensionUsed(NAME);\n    }\n    async onReady() {\n        this._loader.gltf.nodes?.forEach((node) => {\n            if (node.extensions?.KHR_node_selectability && node.extensions?.KHR_node_selectability.selectable === false) {\n                node._babylonTransformNode?.getChildMeshes().forEach((mesh) => {\n                    mesh.isPickable = false;\n                });\n            }\n        });\n    }\n    dispose() {\n        this._loader = null;\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_node_selectability(loader));\n//# sourceMappingURL=KHR_node_selectability.js.map"], "names": ["NAME", "addNewInteractivityFlowGraphMapping", "data", "gltfBlock", "_declaration", "_mapping", "_arrays", "serializedObjects", "context", "globalGLTF", "serializedObject", "nodeIndex", "variableName", "AddObjectAccessorToKey", "node", "tn", "value", "mesh", "KHR_node_selectability", "loader", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "iNAGA,MAAMA,EAAO,yBAEbC,EAAoC,iBAAkBD,EAAM,CAExD,OAAQ,CAAC,8BAAuE,4BAAmE,wBAA2D,6CAA6C,EAC3P,cAAe,CACX,gBAAiB,CAAE,KAAM,iBAAmB,EAC5C,UAAW,CACP,KAAM,WACN,QAAS,4BACT,gBAAgBE,EAAM,CAClB,MAAO,CAAC,cAAgBA,EAAK,CAAC,CAAC,CAClC,CACJ,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,kBAAmB,CAAE,KAAM,QAAS,QAAS,uBAA2D,EACxG,gBAAiB,CAAE,KAAM,WAAa,EACtC,eAAgB,CAAE,KAAM,aAAe,EACvC,mBAAoB,CAAE,KAAM,YAAc,CAC7C,EACD,MAAO,CACH,IAAK,CAAE,KAAM,MAAQ,CACxB,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,QACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,QACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,SACP,OAAQ,aACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeC,EAAWC,EAAcC,EAAUC,EAASC,EAAmBC,EAASC,EAAY,CAE/F,MAAMC,EAAmBH,EAAkBA,EAAkB,OAAS,CAAC,EACvEG,EAAiB,OAASA,EAAiB,QAAU,CAAA,EACrDA,EAAiB,OAAO,KAAOD,EAE/B,MAAME,EAAYR,EAAU,eAAgB,WAAc,MAAM,CAAC,EACjE,GAAIQ,IAAc,QAAa,OAAOA,GAAc,SAChD,MAAM,IAAI,MAAM,sCAAsC,EAE1D,MAAMC,EAAe,cAAgBD,EAErC,OAAAJ,EAAkB,CAAC,EAAE,OAAO,SAAWK,EACvCJ,EAAQ,eAAeI,CAAY,EAAI,CACnC,UAAW,OACX,GAAIH,GAAY,QAAQE,CAAS,EAAE,uBAAuB,GAC1D,SAAUF,GAAY,QAAQE,CAAS,EAAE,uBAAuB,QAC5E,EACeJ,CACV,CACL,CAAC,EAEDM,EAAuB,yDAA0D,CAC7E,IAAMC,GAAS,CACX,MAAMC,EAAKD,EAAK,sBAChB,OAAIC,GAAMA,EAAG,aAAe,OACjBA,EAAG,WAEP,EACV,EACD,IAAK,CAACC,EAAOF,IAAS,CAClBA,EAAK,yBAAyB,QAASG,GAAS,CAC5CA,EAAK,WAAaD,CAC9B,CAAS,CACJ,EACD,UAAYF,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,YAAY,EACpC,KAAM,SACV,CAAC,EAKM,MAAMI,CAAuB,CAIhC,YAAYC,EAAQ,CAIhB,KAAK,KAAOnB,EACZ,KAAK,QAAUmB,EACf,KAAK,QAAUA,EAAO,gBAAgBnB,CAAI,CAC7C,CACD,MAAM,SAAU,CACZ,KAAK,QAAQ,KAAK,OAAO,QAASc,GAAS,CACnCA,EAAK,YAAY,wBAA0BA,EAAK,YAAY,uBAAuB,aAAe,IAClGA,EAAK,uBAAuB,eAAgB,EAAC,QAASG,GAAS,CAC3DA,EAAK,WAAa,EACtC,CAAiB,CAEjB,CAAS,CACJ,CACD,SAAU,CACN,KAAK,QAAU,IAClB,CACL,CACAG,EAAwBpB,CAAI,EAC5BqB,EAAsBrB,EAAM,GAAOmB,GAAW,IAAID,EAAuBC,CAAM,CAAC", "x_google_ignoreList": [0]}