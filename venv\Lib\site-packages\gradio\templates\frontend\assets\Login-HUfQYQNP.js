import G from"./Index-DE1Sah7F.js";import{T as P}from"./Textbox-XjpjeLCp.js";import{B as q}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-DwuBo7pZ.js";/* empty css                                                        */import{$ as J}from"./index-Co_Q4qaw.js";import"./StreamingBar.svelte_svelte_type_style_lang-DlRzv56K.js";/* empty css                                              */import{B as K}from"./Button-DgOo0qHV.js";import Q from"./Index-BsWfuhz5.js";import"./BlockTitle-EkHcTHPF.js";import"./Info-B0PjAnhx.js";import"./MarkdownCode-D_G1OEp4.js";import"./Check-CEkiXcyC.js";import"./Copy-CxQ9EyK2.js";import"./Send-DyoOovnk.js";import"./Square-oAGqOwsh.js";import"./index-DTl--HH3.js";/* empty css                                              */import"./prism-python-PMlrFIzj.js";import"./svelte/svelte.js";import"./Image-CnqB5dbD.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                   */import"./index-CdIv-E5z.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:R,add_flush_callback:j,append:I,attr:B,bind:A,binding_callbacks:E,component_subscribe:U,create_component:v,destroy_component:w,detach:c,element:L,flush:N,init:V,insert:$,mount_component:h,safe_not_equal:W,set_data:D,space:x,text:H,toggle_class:z,transition_in:k,transition_out:T}=window.__gradio__svelte__internal;function C(i){let e;return{c(){e=L("p"),B(e,"class","auth svelte-1ogxbi0")},m(t,o){$(t,e,o),e.innerHTML=i[0]},p(t,o){o&1&&(e.innerHTML=t[0])},d(t){t&&c(e)}}}function F(i){let e,t=i[6]("login.enable_cookies")+"",o;return{c(){e=L("p"),o=H(t),B(e,"class","auth svelte-1ogxbi0")},m(l,s){$(l,e,s),I(e,o)},p(l,s){s&64&&t!==(t=l[6]("login.enable_cookies")+"")&&D(o,t)},d(l){l&&c(e)}}}function O(i){let e,t=i[6]("login.incorrect_credentials")+"",o;return{c(){e=L("p"),o=H(t),B(e,"class","creds svelte-1ogxbi0")},m(l,s){$(l,e,s),I(e,o)},p(l,s){s&64&&t!==(t=l[6]("login.incorrect_credentials")+"")&&D(o,t)},d(l){l&&c(e)}}}function X(i){let e,t,o;function l(n){i[9](n)}let s={label:i[6]("login.username"),lines:1,show_label:!0,max_lines:1};return i[3]!==void 0&&(s.value=i[3]),e=new P({props:s}),E.push(()=>A(e,"value",l)),e.$on("submit",i[7]),{c(){v(e.$$.fragment)},m(n,u){h(e,n,u),o=!0},p(n,u){const f={};u&64&&(f.label=n[6]("login.username")),!t&&u&8&&(t=!0,f.value=n[3],j(()=>t=!1)),e.$set(f)},i(n){o||(k(e.$$.fragment,n),o=!0)},o(n){T(e.$$.fragment,n),o=!1},d(n){w(e,n)}}}function Y(i){let e,t,o;function l(n){i[10](n)}let s={label:i[6]("login.password"),lines:1,show_label:!0,max_lines:1,type:"password"};return i[4]!==void 0&&(s.value=i[4]),e=new P({props:s}),E.push(()=>A(e,"value",l)),e.$on("submit",i[7]),{c(){v(e.$$.fragment)},m(n,u){h(e,n,u),o=!0},p(n,u){const f={};u&64&&(f.label=n[6]("login.password")),!t&&u&16&&(t=!0,f.value=n[4],j(()=>t=!1)),e.$set(f)},i(n){o||(k(e.$$.fragment,n),o=!0)},o(n){T(e.$$.fragment,n),o=!1},d(n){w(e,n)}}}function Z(i){let e,t,o,l;return e=new q({props:{$$slots:{default:[X]},$$scope:{ctx:i}}}),o=new q({props:{$$slots:{default:[Y]},$$scope:{ctx:i}}}),{c(){v(e.$$.fragment),t=x(),v(o.$$.fragment)},m(s,n){h(e,s,n),$(s,t,n),h(o,s,n),l=!0},p(s,n){const u={};n&2120&&(u.$$scope={dirty:n,ctx:s}),e.$set(u);const f={};n&2128&&(f.$$scope={dirty:n,ctx:s}),o.$set(f)},i(s){l||(k(e.$$.fragment,s),k(o.$$.fragment,s),l=!0)},o(s){T(e.$$.fragment,s),T(o.$$.fragment,s),l=!1},d(s){s&&c(t),w(e,s),w(o,s)}}}function y(i){let e=i[6]("login.login")+"",t;return{c(){t=H(e)},m(o,l){$(o,t,l)},p(o,l){l&64&&e!==(e=o[6]("login.login")+"")&&D(t,e)},d(o){o&&c(t)}}}function ee(i){let e,t=i[6]("login.login")+"",o,l,s,n,u,f,b,g,d,p=i[0]&&C(i),_=i[2]&&F(i),a=i[5]&&O(i);return f=new G({props:{$$slots:{default:[Z]},$$scope:{ctx:i}}}),g=new K({props:{size:"lg",variant:"primary",$$slots:{default:[y]},$$scope:{ctx:i}}}),g.$on("click",i[7]),{c(){e=L("h2"),o=H(t),l=x(),p&&p.c(),s=x(),_&&_.c(),n=x(),a&&a.c(),u=x(),v(f.$$.fragment),b=x(),v(g.$$.fragment),B(e,"class","svelte-1ogxbi0")},m(r,m){$(r,e,m),I(e,o),$(r,l,m),p&&p.m(r,m),$(r,s,m),_&&_.m(r,m),$(r,n,m),a&&a.m(r,m),$(r,u,m),h(f,r,m),$(r,b,m),h(g,r,m),d=!0},p(r,m){(!d||m&64)&&t!==(t=r[6]("login.login")+"")&&D(o,t),r[0]?p?p.p(r,m):(p=C(r),p.c(),p.m(s.parentNode,s)):p&&(p.d(1),p=null),r[2]?_?_.p(r,m):(_=F(r),_.c(),_.m(n.parentNode,n)):_&&(_.d(1),_=null),r[5]?a?a.p(r,m):(a=O(r),a.c(),a.m(u.parentNode,u)):a&&(a.d(1),a=null);const M={};m&2136&&(M.$$scope={dirty:m,ctx:r}),f.$set(M);const S={};m&2112&&(S.$$scope={dirty:m,ctx:r}),g.$set(S)},i(r){d||(k(f.$$.fragment,r),k(g.$$.fragment,r),d=!0)},o(r){T(f.$$.fragment,r),T(g.$$.fragment,r),d=!1},d(r){r&&(c(e),c(l),c(s),c(n),c(u),c(b)),p&&p.d(r),_&&_.d(r),a&&a.d(r),w(f,r),w(g,r)}}}function te(i){let e,t,o;return t=new Q({props:{variant:"panel",min_width:480,$$slots:{default:[ee]},$$scope:{ctx:i}}}),{c(){e=L("div"),v(t.$$.fragment),B(e,"class","wrap svelte-1ogxbi0"),z(e,"min-h-screen",i[1])},m(l,s){$(l,e,s),h(t,e,null),o=!0},p(l,[s]){const n={};s&2173&&(n.$$scope={dirty:s,ctx:l}),t.$set(n),(!o||s&2)&&z(e,"min-h-screen",l[1])},i(l){o||(k(t.$$.fragment,l),o=!0)},o(l){T(t.$$.fragment,l),o=!1},d(l){l&&c(e),w(t)}}}function oe(i,e,t){let o;U(i,J,a=>t(6,o=a));let{root:l}=e,{auth_message:s}=e,{app_mode:n}=e,{space_id:u}=e,f="",b="",g=!1;const d=async()=>{const a=new FormData;a.append("username",f),a.append("password",b);let r=await fetch(l+"/login",{method:"POST",body:a});r.status===400?(t(5,g=!0),t(3,f=""),t(4,b="")):r.status==200&&location.reload()};function p(a){f=a,t(3,f)}function _(a){b=a,t(4,b)}return i.$$set=a=>{"root"in a&&t(8,l=a.root),"auth_message"in a&&t(0,s=a.auth_message),"app_mode"in a&&t(1,n=a.app_mode),"space_id"in a&&t(2,u=a.space_id)},[s,n,u,f,b,g,o,d,l,p,_]}class Me extends R{constructor(e){super(),V(this,e,oe,te,W,{root:8,auth_message:0,app_mode:1,space_id:2})}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),N()}get auth_message(){return this.$$.ctx[0]}set auth_message(e){this.$$set({auth_message:e}),N()}get app_mode(){return this.$$.ctx[1]}set app_mode(e){this.$$set({app_mode:e}),N()}get space_id(){return this.$$.ctx[2]}set space_id(e){this.$$set({space_id:e}),N()}}export{Me as default};
//# sourceMappingURL=Login-HUfQYQNP.js.map
