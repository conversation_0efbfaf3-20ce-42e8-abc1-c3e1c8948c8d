{"version": 3, "file": "KHR_lights_punctual-CAVQfrhV.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Lights/directionalLight.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Lights/pointLight.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_lights_punctual.js"], "sourcesContent": ["import { __decorate } from \"../tslib.es6.js\";\nimport { serialize } from \"../Misc/decorators.js\";\nimport { Matrix, Vector3 } from \"../Maths/math.vector.js\";\nimport { Node } from \"../node.js\";\nimport { Light } from \"./light.js\";\nimport { ShadowLight } from \"./shadowLight.js\";\nimport { RegisterClass } from \"../Misc/typeStore.js\";\n\nNode.AddNodeConstructor(\"Light_Type_1\", (name, scene) => {\n    return () => new DirectionalLight(name, Vector3.Zero(), scene);\n});\n/**\n * A directional light is defined by a direction (what a surprise!).\n * The light is emitted from everywhere in the specified direction, and has an infinite range.\n * An example of a directional light is when a distance planet is lit by the apparently parallel lines of light from its sun. Light in a downward direction will light the top of an object.\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\n */\nexport class DirectionalLight extends ShadowLight {\n    /**\n     * Fix frustum size for the shadow generation. This is disabled if the value is 0.\n     */\n    get shadowFrustumSize() {\n        return this._shadowFrustumSize;\n    }\n    /**\n     * Specifies a fix frustum size for the shadow generation.\n     */\n    set shadowFrustumSize(value) {\n        this._shadowFrustumSize = value;\n        this.forceProjectionMatrixCompute();\n    }\n    /**\n     * Gets the shadow projection scale against the optimal computed one.\n     * 0.1 by default which means that the projection window is increase by 10% from the optimal size.\n     * This does not impact in fixed frustum size (shadowFrustumSize being set)\n     */\n    get shadowOrthoScale() {\n        return this._shadowOrthoScale;\n    }\n    /**\n     * Sets the shadow projection scale against the optimal computed one.\n     * 0.1 by default which means that the projection window is increase by 10% from the optimal size.\n     * This does not impact in fixed frustum size (shadowFrustumSize being set)\n     */\n    set shadowOrthoScale(value) {\n        this._shadowOrthoScale = value;\n        this.forceProjectionMatrixCompute();\n    }\n    /**\n     * Gets or sets the orthoLeft property used to build the light frustum\n     */\n    get orthoLeft() {\n        return this._orthoLeft;\n    }\n    set orthoLeft(left) {\n        this._orthoLeft = left;\n    }\n    /**\n     * Gets or sets the orthoRight property used to build the light frustum\n     */\n    get orthoRight() {\n        return this._orthoRight;\n    }\n    set orthoRight(right) {\n        this._orthoRight = right;\n    }\n    /**\n     * Gets or sets the orthoTop property used to build the light frustum\n     */\n    get orthoTop() {\n        return this._orthoTop;\n    }\n    set orthoTop(top) {\n        this._orthoTop = top;\n    }\n    /**\n     * Gets or sets the orthoBottom property used to build the light frustum\n     */\n    get orthoBottom() {\n        return this._orthoBottom;\n    }\n    set orthoBottom(bottom) {\n        this._orthoBottom = bottom;\n    }\n    /**\n     * Creates a DirectionalLight object in the scene, oriented towards the passed direction (Vector3).\n     * The directional light is emitted from everywhere in the given direction.\n     * It can cast shadows.\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\n     * @param name The friendly name of the light\n     * @param direction The direction of the light\n     * @param scene The scene the light belongs to\n     */\n    constructor(name, direction, scene) {\n        super(name, scene);\n        this._shadowFrustumSize = 0;\n        this._shadowOrthoScale = 0.1;\n        /**\n         * Automatically compute the projection matrix to best fit (including all the casters)\n         * on each frame.\n         */\n        this.autoUpdateExtends = true;\n        /**\n         * Automatically compute the shadowMinZ and shadowMaxZ for the projection matrix to best fit (including all the casters)\n         * on each frame. autoUpdateExtends must be set to true for this to work\n         */\n        this.autoCalcShadowZBounds = false;\n        // Cache\n        this._orthoLeft = Number.MAX_VALUE;\n        this._orthoRight = Number.MIN_VALUE;\n        this._orthoTop = Number.MIN_VALUE;\n        this._orthoBottom = Number.MAX_VALUE;\n        this.position = direction.scale(-1.0);\n        this.direction = direction;\n    }\n    /**\n     * Returns the string \"DirectionalLight\".\n     * @returns The class name\n     */\n    getClassName() {\n        return \"DirectionalLight\";\n    }\n    /**\n     * Returns the integer 1.\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\n     */\n    getTypeID() {\n        return Light.LIGHTTYPEID_DIRECTIONALLIGHT;\n    }\n    /**\n     * Sets the passed matrix \"matrix\" as projection matrix for the shadows cast by the light according to the passed view matrix.\n     * Returns the DirectionalLight Shadow projection matrix.\n     * @param matrix\n     * @param viewMatrix\n     * @param renderList\n     */\n    _setDefaultShadowProjectionMatrix(matrix, viewMatrix, renderList) {\n        if (this.shadowFrustumSize > 0) {\n            this._setDefaultFixedFrustumShadowProjectionMatrix(matrix);\n        }\n        else {\n            this._setDefaultAutoExtendShadowProjectionMatrix(matrix, viewMatrix, renderList);\n        }\n    }\n    /**\n     * Sets the passed matrix \"matrix\" as fixed frustum projection matrix for the shadows cast by the light according to the passed view matrix.\n     * Returns the DirectionalLight Shadow projection matrix.\n     * @param matrix\n     */\n    _setDefaultFixedFrustumShadowProjectionMatrix(matrix) {\n        const activeCamera = this.getScene().activeCamera;\n        if (!activeCamera) {\n            return;\n        }\n        Matrix.OrthoLHToRef(this.shadowFrustumSize, this.shadowFrustumSize, this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ, this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ, matrix, this.getScene().getEngine().isNDCHalfZRange);\n    }\n    /**\n     * Sets the passed matrix \"matrix\" as auto extend projection matrix for the shadows cast by the light according to the passed view matrix.\n     * Returns the DirectionalLight Shadow projection matrix.\n     * @param matrix\n     * @param viewMatrix\n     * @param renderList\n     */\n    _setDefaultAutoExtendShadowProjectionMatrix(matrix, viewMatrix, renderList) {\n        const activeCamera = this.getScene().activeCamera;\n        // Check extends\n        if (this.autoUpdateExtends || this._orthoLeft === Number.MAX_VALUE) {\n            const tempVector3 = Vector3.Zero();\n            this._orthoLeft = Number.MAX_VALUE;\n            this._orthoRight = -Number.MAX_VALUE;\n            this._orthoTop = -Number.MAX_VALUE;\n            this._orthoBottom = Number.MAX_VALUE;\n            let shadowMinZ = Number.MAX_VALUE;\n            let shadowMaxZ = -Number.MAX_VALUE;\n            for (let meshIndex = 0; meshIndex < renderList.length; meshIndex++) {\n                const mesh = renderList[meshIndex];\n                if (!mesh) {\n                    continue;\n                }\n                const boundingInfo = mesh.getBoundingInfo();\n                const boundingBox = boundingInfo.boundingBox;\n                for (let index = 0; index < boundingBox.vectorsWorld.length; index++) {\n                    Vector3.TransformCoordinatesToRef(boundingBox.vectorsWorld[index], viewMatrix, tempVector3);\n                    if (tempVector3.x < this._orthoLeft) {\n                        this._orthoLeft = tempVector3.x;\n                    }\n                    if (tempVector3.y < this._orthoBottom) {\n                        this._orthoBottom = tempVector3.y;\n                    }\n                    if (tempVector3.x > this._orthoRight) {\n                        this._orthoRight = tempVector3.x;\n                    }\n                    if (tempVector3.y > this._orthoTop) {\n                        this._orthoTop = tempVector3.y;\n                    }\n                    if (this.autoCalcShadowZBounds) {\n                        if (tempVector3.z < shadowMinZ) {\n                            shadowMinZ = tempVector3.z;\n                        }\n                        if (tempVector3.z > shadowMaxZ) {\n                            shadowMaxZ = tempVector3.z;\n                        }\n                    }\n                }\n            }\n            if (this.autoCalcShadowZBounds) {\n                this._shadowMinZ = shadowMinZ;\n                this._shadowMaxZ = shadowMaxZ;\n            }\n        }\n        const xOffset = this._orthoRight - this._orthoLeft;\n        const yOffset = this._orthoTop - this._orthoBottom;\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera?.minZ || 0;\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera?.maxZ || 10000;\n        const useReverseDepthBuffer = this.getScene().getEngine().useReverseDepthBuffer;\n        Matrix.OrthoOffCenterLHToRef(this._orthoLeft - xOffset * this.shadowOrthoScale, this._orthoRight + xOffset * this.shadowOrthoScale, this._orthoBottom - yOffset * this.shadowOrthoScale, this._orthoTop + yOffset * this.shadowOrthoScale, useReverseDepthBuffer ? maxZ : minZ, useReverseDepthBuffer ? minZ : maxZ, matrix, this.getScene().getEngine().isNDCHalfZRange);\n    }\n    _buildUniformLayout() {\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\n        this._uniformBuffer.create();\n    }\n    /**\n     * Sets the passed Effect object with the DirectionalLight transformed position (or position if not parented) and the passed name.\n     * @param effect The effect to update\n     * @param lightIndex The index of the light in the effect to update\n     * @returns The directional light\n     */\n    transferToEffect(effect, lightIndex) {\n        if (this.computeTransformedInformation()) {\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.transformedDirection.x, this.transformedDirection.y, this.transformedDirection.z, 1, lightIndex);\n            return this;\n        }\n        this._uniformBuffer.updateFloat4(\"vLightData\", this.direction.x, this.direction.y, this.direction.z, 1, lightIndex);\n        return this;\n    }\n    transferToNodeMaterialEffect(effect, lightDataUniformName) {\n        if (this.computeTransformedInformation()) {\n            effect.setFloat3(lightDataUniformName, this.transformedDirection.x, this.transformedDirection.y, this.transformedDirection.z);\n            return this;\n        }\n        effect.setFloat3(lightDataUniformName, this.direction.x, this.direction.y, this.direction.z);\n        return this;\n    }\n    /**\n     * Gets the minZ used for shadow according to both the scene and the light.\n     *\n     * Values are fixed on directional lights as it relies on an ortho projection hence the need to convert being\n     * -1 and 1 to 0 and 1 doing (depth + min) / (min + max) -> (depth + 1) / (1 + 1) -> (depth * 0.5) + 0.5.\n     * (when not using reverse depth buffer / NDC half Z range)\n     * @param _activeCamera The camera we are returning the min for (not used)\n     * @returns the depth min z\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    getDepthMinZ(_activeCamera) {\n        const engine = this._scene.getEngine();\n        return !engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? 0 : 1;\n    }\n    /**\n     * Gets the maxZ used for shadow according to both the scene and the light.\n     *\n     * Values are fixed on directional lights as it relies on an ortho projection hence the need to convert being\n     * -1 and 1 to 0 and 1 doing (depth + min) / (min + max) -> (depth + 1) / (1 + 1) -> (depth * 0.5) + 0.5.\n     * (when not using reverse depth buffer / NDC half Z range)\n     * @param _activeCamera The camera we are returning the max for\n     * @returns the depth max z\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    getDepthMaxZ(_activeCamera) {\n        const engine = this._scene.getEngine();\n        return engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? 0 : 1;\n    }\n    /**\n     * Prepares the list of defines specific to the light type.\n     * @param defines the list of defines\n     * @param lightIndex defines the index of the light for the effect\n     */\n    prepareLightSpecificDefines(defines, lightIndex) {\n        defines[\"DIRLIGHT\" + lightIndex] = true;\n    }\n}\n__decorate([\n    serialize()\n], DirectionalLight.prototype, \"shadowFrustumSize\", null);\n__decorate([\n    serialize()\n], DirectionalLight.prototype, \"shadowOrthoScale\", null);\n__decorate([\n    serialize()\n], DirectionalLight.prototype, \"autoUpdateExtends\", void 0);\n__decorate([\n    serialize()\n], DirectionalLight.prototype, \"autoCalcShadowZBounds\", void 0);\n__decorate([\n    serialize(\"orthoLeft\")\n], DirectionalLight.prototype, \"_orthoLeft\", void 0);\n__decorate([\n    serialize(\"orthoRight\")\n], DirectionalLight.prototype, \"_orthoRight\", void 0);\n__decorate([\n    serialize(\"orthoTop\")\n], DirectionalLight.prototype, \"_orthoTop\", void 0);\n__decorate([\n    serialize(\"orthoBottom\")\n], DirectionalLight.prototype, \"_orthoBottom\", void 0);\n// Register Class Name\nRegisterClass(\"BABYLON.DirectionalLight\", DirectionalLight);\n//# sourceMappingURL=directionalLight.js.map", "import { __decorate } from \"../tslib.es6.js\";\nimport { serialize } from \"../Misc/decorators.js\";\nimport { Matrix, Vector3 } from \"../Maths/math.vector.js\";\nimport { Node } from \"../node.js\";\nimport { Light } from \"./light.js\";\nimport { ShadowLight } from \"./shadowLight.js\";\nimport { RegisterClass } from \"../Misc/typeStore.js\";\nNode.AddNodeConstructor(\"Light_Type_0\", (name, scene) => {\n    return () => new PointLight(name, Vector3.Zero(), scene);\n});\n/**\n * A point light is a light defined by an unique point in world space.\n * The light is emitted in every direction from this point.\n * A good example of a point light is a standard light bulb.\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\n */\nexport class PointLight extends ShadowLight {\n    /**\n     * Getter: In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\n     * This specifies what angle the shadow will use to be created.\n     *\n     * It default to 90 degrees to work nicely with the cube texture generation for point lights shadow maps.\n     */\n    get shadowAngle() {\n        return this._shadowAngle;\n    }\n    /**\n     * Setter: In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\n     * This specifies what angle the shadow will use to be created.\n     *\n     * It default to 90 degrees to work nicely with the cube texture generation for point lights shadow maps.\n     */\n    set shadowAngle(value) {\n        this._shadowAngle = value;\n        this.forceProjectionMatrixCompute();\n    }\n    /**\n     * Gets the direction if it has been set.\n     * In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\n     */\n    get direction() {\n        return this._direction;\n    }\n    /**\n     * In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\n     */\n    set direction(value) {\n        const previousNeedCube = this.needCube();\n        this._direction = value;\n        if (this.needCube() !== previousNeedCube && this._shadowGenerators) {\n            const iterator = this._shadowGenerators.values();\n            for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\n                const shadowGenerator = key.value;\n                shadowGenerator.recreateShadowMap();\n            }\n        }\n    }\n    /**\n     * Creates a PointLight object from the passed name and position (Vector3) and adds it in the scene.\n     * A PointLight emits the light in every direction.\n     * It can cast shadows.\n     * If the scene camera is already defined and you want to set your PointLight at the camera position, just set it :\n     * ```javascript\n     * var pointLight = new PointLight(\"pl\", camera.position, scene);\n     * ```\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\n     * @param name The light friendly name\n     * @param position The position of the point light in the scene\n     * @param scene The scene the lights belongs to\n     */\n    constructor(name, position, scene) {\n        super(name, scene);\n        this._shadowAngle = Math.PI / 2;\n        this.position = position;\n    }\n    /**\n     * Returns the string \"PointLight\"\n     * @returns the class name\n     */\n    getClassName() {\n        return \"PointLight\";\n    }\n    /**\n     * Returns the integer 0.\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\n     */\n    getTypeID() {\n        return Light.LIGHTTYPEID_POINTLIGHT;\n    }\n    /**\n     * Specifies whether or not the shadowmap should be a cube texture.\n     * @returns true if the shadowmap needs to be a cube texture.\n     */\n    needCube() {\n        return !this.direction;\n    }\n    /**\n     * Returns a new Vector3 aligned with the PointLight cube system according to the passed cube face index (integer).\n     * @param faceIndex The index of the face we are computed the direction to generate shadow\n     * @returns The set direction in 2d mode otherwise the direction to the cubemap face if needCube() is true\n     */\n    getShadowDirection(faceIndex) {\n        if (this.direction) {\n            return super.getShadowDirection(faceIndex);\n        }\n        else {\n            switch (faceIndex) {\n                case 0:\n                    return new Vector3(1.0, 0.0, 0.0);\n                case 1:\n                    return new Vector3(-1.0, 0.0, 0.0);\n                case 2:\n                    return new Vector3(0.0, -1.0, 0.0);\n                case 3:\n                    return new Vector3(0.0, 1.0, 0.0);\n                case 4:\n                    return new Vector3(0.0, 0.0, 1.0);\n                case 5:\n                    return new Vector3(0.0, 0.0, -1.0);\n            }\n        }\n        return Vector3.Zero();\n    }\n    /**\n     * Sets the passed matrix \"matrix\" as a left-handed perspective projection matrix with the following settings :\n     * - fov = PI / 2\n     * - aspect ratio : 1.0\n     * - z-near and far equal to the active camera minZ and maxZ.\n     * Returns the PointLight.\n     * @param matrix\n     * @param viewMatrix\n     * @param renderList\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _setDefaultShadowProjectionMatrix(matrix, viewMatrix, renderList) {\n        const activeCamera = this.getScene().activeCamera;\n        if (!activeCamera) {\n            return;\n        }\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ;\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ;\n        const useReverseDepthBuffer = this.getScene().getEngine().useReverseDepthBuffer;\n        Matrix.PerspectiveFovLHToRef(this.shadowAngle, 1.0, useReverseDepthBuffer ? maxZ : minZ, useReverseDepthBuffer ? minZ : maxZ, matrix, true, this._scene.getEngine().isNDCHalfZRange, undefined, useReverseDepthBuffer);\n    }\n    _buildUniformLayout() {\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\n        this._uniformBuffer.addUniform(\"vLightFalloff\", 4);\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\n        this._uniformBuffer.create();\n    }\n    /**\n     * Sets the passed Effect \"effect\" with the PointLight transformed position (or position, if none) and passed name (string).\n     * @param effect The effect to update\n     * @param lightIndex The index of the light in the effect to update\n     * @returns The point light\n     */\n    transferToEffect(effect, lightIndex) {\n        if (this.computeTransformedInformation()) {\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z, 0.0, lightIndex);\n        }\n        else {\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.position.x, this.position.y, this.position.z, 0, lightIndex);\n        }\n        this._uniformBuffer.updateFloat4(\"vLightFalloff\", this.range, this._inverseSquaredRange, 0, 0, lightIndex);\n        return this;\n    }\n    transferToNodeMaterialEffect(effect, lightDataUniformName) {\n        if (this.computeTransformedInformation()) {\n            effect.setFloat3(lightDataUniformName, this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z);\n        }\n        else {\n            effect.setFloat3(lightDataUniformName, this.position.x, this.position.y, this.position.z);\n        }\n        return this;\n    }\n    /**\n     * Prepares the list of defines specific to the light type.\n     * @param defines the list of defines\n     * @param lightIndex defines the index of the light for the effect\n     */\n    prepareLightSpecificDefines(defines, lightIndex) {\n        defines[\"POINTLIGHT\" + lightIndex] = true;\n    }\n}\n__decorate([\n    serialize()\n], PointLight.prototype, \"shadowAngle\", null);\n// Register Class Name\nRegisterClass(\"BABYLON.PointLight\", PointLight);\n//# sourceMappingURL=pointLight.js.map", "import { Vector3 } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { DirectionalLight } from \"@babylonjs/core/Lights/directionalLight.js\";\nimport { PointLight } from \"@babylonjs/core/Lights/pointLight.js\";\nimport { SpotLight } from \"@babylonjs/core/Lights/spotLight.js\";\nimport { Light } from \"@babylonjs/core/Lights/light.js\";\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_lights_punctual\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_lights_punctual/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_lights {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n        delete this._lights;\n    }\n    /** @internal */\n    onLoading() {\n        const extensions = this._loader.gltf.extensions;\n        if (extensions && extensions[this.name]) {\n            const extension = extensions[this.name];\n            this._lights = extension.lights;\n            ArrayItem.Assign(this._lights);\n        }\n    }\n    /**\n     * @internal\n     */\n    loadNodeAsync(context, node, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, node, this.name, (extensionContext, extension) => {\n            this._loader._allMaterialsDirtyRequired = true;\n            return this._loader.loadNodeAsync(context, node, (babylonMesh) => {\n                let babylonLight;\n                const light = ArrayItem.Get(extensionContext, this._lights, extension.light);\n                const name = light.name || babylonMesh.name;\n                this._loader.babylonScene._blockEntityCollection = !!this._loader._assetContainer;\n                switch (light.type) {\n                    case \"directional\" /* KHRLightsPunctual_LightType.DIRECTIONAL */: {\n                        const babylonDirectionalLight = new DirectionalLight(name, Vector3.Backward(), this._loader.babylonScene);\n                        babylonDirectionalLight.position.setAll(0);\n                        babylonLight = babylonDirectionalLight;\n                        break;\n                    }\n                    case \"point\" /* KHRLightsPunctual_LightType.POINT */: {\n                        babylonLight = new PointLight(name, Vector3.Zero(), this._loader.babylonScene);\n                        break;\n                    }\n                    case \"spot\" /* KHRLightsPunctual_LightType.SPOT */: {\n                        const babylonSpotLight = new SpotLight(name, Vector3.Zero(), Vector3.Backward(), 0, 1, this._loader.babylonScene);\n                        babylonSpotLight.angle = ((light.spot && light.spot.outerConeAngle) || Math.PI / 4) * 2;\n                        babylonSpotLight.innerAngle = ((light.spot && light.spot.innerConeAngle) || 0) * 2;\n                        babylonLight = babylonSpotLight;\n                        break;\n                    }\n                    default: {\n                        this._loader.babylonScene._blockEntityCollection = false;\n                        throw new Error(`${extensionContext}: Invalid light type (${light.type})`);\n                    }\n                }\n                babylonLight._parentContainer = this._loader._assetContainer;\n                this._loader.babylonScene._blockEntityCollection = false;\n                light._babylonLight = babylonLight;\n                babylonLight.falloffType = Light.FALLOFF_GLTF;\n                babylonLight.diffuse = light.color ? Color3.FromArray(light.color) : Color3.White();\n                babylonLight.intensity = light.intensity == undefined ? 1 : light.intensity;\n                babylonLight.range = light.range == undefined ? Number.MAX_VALUE : light.range;\n                babylonLight.parent = babylonMesh;\n                this._loader._babylonLights.push(babylonLight);\n                GLTFLoader.AddPointerMetadata(babylonLight, extensionContext);\n                assign(babylonMesh);\n            });\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_lights(loader));\n//# sourceMappingURL=KHR_lights_punctual.js.map"], "names": ["Node", "name", "scene", "DirectionalLight", "Vector3", "ShadowLight", "value", "left", "right", "top", "bottom", "direction", "Light", "matrix", "viewMatrix", "renderList", "activeCamera", "Matrix", "tempVector3", "shadowMinZ", "shadowMaxZ", "meshIndex", "mesh", "boundingBox", "index", "xOffset", "yOffset", "minZ", "maxZ", "useReverseDepthBuffer", "effect", "lightIndex", "lightDataUniformName", "_activeCamera", "engine", "defines", "__decorate", "serialize", "RegisterClass", "PointLight", "previousNeedCube", "iterator", "key", "position", "faceIndex", "NAME", "KHR_lights", "loader", "extensions", "extension", "ArrayItem", "context", "node", "assign", "GLTFLoader", "extensionContext", "<PERSON><PERSON><PERSON><PERSON>", "babylonLight", "light", "babylonDirectionalLight", "babylonSpotLight", "SpotLight", "Color3", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "mYAQAA,EAAK,mBAAmB,eAAgB,CAACC,EAAMC,IACpC,IAAM,IAAIC,EAAiBF,EAAMG,EAAQ,KAAI,EAAIF,CAAK,CAChE,EAOM,MAAMC,UAAyBE,CAAY,CAI9C,IAAI,mBAAoB,CACpB,OAAO,KAAK,kBACf,CAID,IAAI,kBAAkBC,EAAO,CACzB,KAAK,mBAAqBA,EAC1B,KAAK,6BAA4B,CACpC,CAMD,IAAI,kBAAmB,CACnB,OAAO,KAAK,iBACf,CAMD,IAAI,iBAAiBA,EAAO,CACxB,KAAK,kBAAoBA,EACzB,KAAK,6BAA4B,CACpC,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CACD,IAAI,UAAUC,EAAM,CAChB,KAAK,WAAaA,CACrB,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CACD,IAAI,WAAWC,EAAO,CAClB,KAAK,YAAcA,CACtB,CAID,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CACD,IAAI,SAASC,EAAK,CACd,KAAK,UAAYA,CACpB,CAID,IAAI,aAAc,CACd,OAAO,KAAK,YACf,CACD,IAAI,YAAYC,EAAQ,CACpB,KAAK,aAAeA,CACvB,CAUD,YAAYT,EAAMU,EAAWT,EAAO,CAChC,MAAMD,EAAMC,CAAK,EACjB,KAAK,mBAAqB,EAC1B,KAAK,kBAAoB,GAKzB,KAAK,kBAAoB,GAKzB,KAAK,sBAAwB,GAE7B,KAAK,WAAa,OAAO,UACzB,KAAK,YAAc,OAAO,UAC1B,KAAK,UAAY,OAAO,UACxB,KAAK,aAAe,OAAO,UAC3B,KAAK,SAAWS,EAAU,MAAM,EAAI,EACpC,KAAK,UAAYA,CACpB,CAKD,cAAe,CACX,MAAO,kBACV,CAKD,WAAY,CACR,OAAOC,EAAM,4BAChB,CAQD,kCAAkCC,EAAQC,EAAYC,EAAY,CAC1D,KAAK,kBAAoB,EACzB,KAAK,8CAA8CF,CAAM,EAGzD,KAAK,4CAA4CA,EAAQC,EAAYC,CAAU,CAEtF,CAMD,8CAA8CF,EAAQ,CAClD,MAAMG,EAAe,KAAK,SAAQ,EAAG,aAChCA,GAGLC,EAAO,aAAa,KAAK,kBAAmB,KAAK,kBAAmB,KAAK,aAAe,OAAY,KAAK,WAAaD,EAAa,KAAM,KAAK,aAAe,OAAY,KAAK,WAAaA,EAAa,KAAMH,EAAQ,KAAK,SAAU,EAAC,UAAW,EAAC,eAAe,CACpQ,CAQD,4CAA4CA,EAAQC,EAAYC,EAAY,CACxE,MAAMC,EAAe,KAAK,SAAQ,EAAG,aAErC,GAAI,KAAK,mBAAqB,KAAK,aAAe,OAAO,UAAW,CAChE,MAAME,EAAcd,EAAQ,OAC5B,KAAK,WAAa,OAAO,UACzB,KAAK,YAAc,CAAC,OAAO,UAC3B,KAAK,UAAY,CAAC,OAAO,UACzB,KAAK,aAAe,OAAO,UAC3B,IAAIe,EAAa,OAAO,UACpBC,EAAa,CAAC,OAAO,UACzB,QAASC,EAAY,EAAGA,EAAYN,EAAW,OAAQM,IAAa,CAChE,MAAMC,EAAOP,EAAWM,CAAS,EACjC,GAAI,CAACC,EACD,SAGJ,MAAMC,EADeD,EAAK,kBACO,YACjC,QAASE,EAAQ,EAAGA,EAAQD,EAAY,aAAa,OAAQC,IACzDpB,EAAQ,0BAA0BmB,EAAY,aAAaC,CAAK,EAAGV,EAAYI,CAAW,EACtFA,EAAY,EAAI,KAAK,aACrB,KAAK,WAAaA,EAAY,GAE9BA,EAAY,EAAI,KAAK,eACrB,KAAK,aAAeA,EAAY,GAEhCA,EAAY,EAAI,KAAK,cACrB,KAAK,YAAcA,EAAY,GAE/BA,EAAY,EAAI,KAAK,YACrB,KAAK,UAAYA,EAAY,GAE7B,KAAK,wBACDA,EAAY,EAAIC,IAChBA,EAAaD,EAAY,GAEzBA,EAAY,EAAIE,IAChBA,EAAaF,EAAY,GAIxC,CACG,KAAK,wBACL,KAAK,YAAcC,EACnB,KAAK,YAAcC,EAE1B,CACD,MAAMK,EAAU,KAAK,YAAc,KAAK,WAClCC,EAAU,KAAK,UAAY,KAAK,aAChCC,EAAO,KAAK,aAAe,OAAY,KAAK,WAAaX,GAAc,MAAQ,EAC/EY,EAAO,KAAK,aAAe,OAAY,KAAK,WAAaZ,GAAc,MAAQ,IAC/Ea,EAAwB,KAAK,SAAU,EAAC,UAAS,EAAG,sBAC1DZ,EAAO,sBAAsB,KAAK,WAAaQ,EAAU,KAAK,iBAAkB,KAAK,YAAcA,EAAU,KAAK,iBAAkB,KAAK,aAAeC,EAAU,KAAK,iBAAkB,KAAK,UAAYA,EAAU,KAAK,iBAAkBG,EAAwBD,EAAOD,EAAME,EAAwBF,EAAOC,EAAMf,EAAQ,KAAK,SAAU,EAAC,UAAS,EAAG,eAAe,CAC3W,CACD,qBAAsB,CAClB,KAAK,eAAe,WAAW,aAAc,CAAC,EAC9C,KAAK,eAAe,WAAW,gBAAiB,CAAC,EACjD,KAAK,eAAe,WAAW,iBAAkB,CAAC,EAClD,KAAK,eAAe,WAAW,cAAe,CAAC,EAC/C,KAAK,eAAe,WAAW,cAAe,CAAC,EAC/C,KAAK,eAAe,QACvB,CAOD,iBAAiBiB,EAAQC,EAAY,CACjC,OAAI,KAAK,iCACL,KAAK,eAAe,aAAa,aAAc,KAAK,qBAAqB,EAAG,KAAK,qBAAqB,EAAG,KAAK,qBAAqB,EAAG,EAAGA,CAAU,EAC5I,OAEX,KAAK,eAAe,aAAa,aAAc,KAAK,UAAU,EAAG,KAAK,UAAU,EAAG,KAAK,UAAU,EAAG,EAAGA,CAAU,EAC3G,KACV,CACD,6BAA6BD,EAAQE,EAAsB,CACvD,OAAI,KAAK,iCACLF,EAAO,UAAUE,EAAsB,KAAK,qBAAqB,EAAG,KAAK,qBAAqB,EAAG,KAAK,qBAAqB,CAAC,EACrH,OAEXF,EAAO,UAAUE,EAAsB,KAAK,UAAU,EAAG,KAAK,UAAU,EAAG,KAAK,UAAU,CAAC,EACpF,KACV,CAWD,aAAaC,EAAe,CACxB,MAAMC,EAAS,KAAK,OAAO,UAAS,EACpC,MAAO,CAACA,EAAO,uBAAyBA,EAAO,gBAAkB,EAAI,CACxE,CAWD,aAAaD,EAAe,CACxB,MAAMC,EAAS,KAAK,OAAO,UAAS,EACpC,OAAOA,EAAO,uBAAyBA,EAAO,gBAAkB,EAAI,CACvE,CAMD,4BAA4BC,EAASJ,EAAY,CAC7CI,EAAQ,WAAaJ,CAAU,EAAI,EACtC,CACL,CACAK,EAAW,CACPC,EAAW,CACf,EAAGlC,EAAiB,UAAW,oBAAqB,IAAI,EACxDiC,EAAW,CACPC,EAAW,CACf,EAAGlC,EAAiB,UAAW,mBAAoB,IAAI,EACvDiC,EAAW,CACPC,EAAW,CACf,EAAGlC,EAAiB,UAAW,oBAAqB,MAAM,EAC1DiC,EAAW,CACPC,EAAW,CACf,EAAGlC,EAAiB,UAAW,wBAAyB,MAAM,EAC9DiC,EAAW,CACPC,EAAU,WAAW,CACzB,EAAGlC,EAAiB,UAAW,aAAc,MAAM,EACnDiC,EAAW,CACPC,EAAU,YAAY,CAC1B,EAAGlC,EAAiB,UAAW,cAAe,MAAM,EACpDiC,EAAW,CACPC,EAAU,UAAU,CACxB,EAAGlC,EAAiB,UAAW,YAAa,MAAM,EAClDiC,EAAW,CACPC,EAAU,aAAa,CAC3B,EAAGlC,EAAiB,UAAW,eAAgB,MAAM,EAErDmC,EAAc,2BAA4BnC,CAAgB,EC9S1DH,EAAK,mBAAmB,eAAgB,CAACC,EAAMC,IACpC,IAAM,IAAIqC,EAAWtC,EAAMG,EAAQ,KAAI,EAAIF,CAAK,CAC1D,EAOM,MAAMqC,UAAmBlC,CAAY,CAOxC,IAAI,aAAc,CACd,OAAO,KAAK,YACf,CAOD,IAAI,YAAYC,EAAO,CACnB,KAAK,aAAeA,EACpB,KAAK,6BAA4B,CACpC,CAKD,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CAID,IAAI,UAAUA,EAAO,CACjB,MAAMkC,EAAmB,KAAK,WAE9B,GADA,KAAK,WAAalC,EACd,KAAK,SAAQ,IAAOkC,GAAoB,KAAK,kBAAmB,CAChE,MAAMC,EAAW,KAAK,kBAAkB,OAAM,EAC9C,QAASC,EAAMD,EAAS,OAAQC,EAAI,OAAS,GAAMA,EAAMD,EAAS,KAAI,EAC1CC,EAAI,MACZ,kBAAiB,CAExC,CACJ,CAcD,YAAYzC,EAAM0C,EAAUzC,EAAO,CAC/B,MAAMD,EAAMC,CAAK,EACjB,KAAK,aAAe,KAAK,GAAK,EAC9B,KAAK,SAAWyC,CACnB,CAKD,cAAe,CACX,MAAO,YACV,CAKD,WAAY,CACR,OAAO/B,EAAM,sBAChB,CAKD,UAAW,CACP,MAAO,CAAC,KAAK,SAChB,CAMD,mBAAmBgC,EAAW,CAC1B,GAAI,KAAK,UACL,OAAO,MAAM,mBAAmBA,CAAS,EAGzC,OAAQA,EAAS,CACb,IAAK,GACD,OAAO,IAAIxC,EAAQ,EAAK,EAAK,CAAG,EACpC,IAAK,GACD,OAAO,IAAIA,EAAQ,GAAM,EAAK,CAAG,EACrC,IAAK,GACD,OAAO,IAAIA,EAAQ,EAAK,GAAM,CAAG,EACrC,IAAK,GACD,OAAO,IAAIA,EAAQ,EAAK,EAAK,CAAG,EACpC,IAAK,GACD,OAAO,IAAIA,EAAQ,EAAK,EAAK,CAAG,EACpC,IAAK,GACD,OAAO,IAAIA,EAAQ,EAAK,EAAK,EAAI,CACxC,CAEL,OAAOA,EAAQ,MAClB,CAYD,kCAAkCS,EAAQC,EAAYC,EAAY,CAC9D,MAAMC,EAAe,KAAK,SAAQ,EAAG,aACrC,GAAI,CAACA,EACD,OAEJ,MAAMW,EAAO,KAAK,aAAe,OAAY,KAAK,WAAaX,EAAa,KACtEY,EAAO,KAAK,aAAe,OAAY,KAAK,WAAaZ,EAAa,KACtEa,EAAwB,KAAK,SAAU,EAAC,UAAS,EAAG,sBAC1DZ,EAAO,sBAAsB,KAAK,YAAa,EAAKY,EAAwBD,EAAOD,EAAME,EAAwBF,EAAOC,EAAMf,EAAQ,GAAM,KAAK,OAAO,UAAS,EAAG,gBAAiB,OAAWgB,CAAqB,CACxN,CACD,qBAAsB,CAClB,KAAK,eAAe,WAAW,aAAc,CAAC,EAC9C,KAAK,eAAe,WAAW,gBAAiB,CAAC,EACjD,KAAK,eAAe,WAAW,iBAAkB,CAAC,EAClD,KAAK,eAAe,WAAW,gBAAiB,CAAC,EACjD,KAAK,eAAe,WAAW,cAAe,CAAC,EAC/C,KAAK,eAAe,WAAW,cAAe,CAAC,EAC/C,KAAK,eAAe,QACvB,CAOD,iBAAiBC,EAAQC,EAAY,CACjC,OAAI,KAAK,gCACL,KAAK,eAAe,aAAa,aAAc,KAAK,oBAAoB,EAAG,KAAK,oBAAoB,EAAG,KAAK,oBAAoB,EAAG,EAAKA,CAAU,EAGlJ,KAAK,eAAe,aAAa,aAAc,KAAK,SAAS,EAAG,KAAK,SAAS,EAAG,KAAK,SAAS,EAAG,EAAGA,CAAU,EAEnH,KAAK,eAAe,aAAa,gBAAiB,KAAK,MAAO,KAAK,qBAAsB,EAAG,EAAGA,CAAU,EAClG,IACV,CACD,6BAA6BD,EAAQE,EAAsB,CACvD,OAAI,KAAK,gCACLF,EAAO,UAAUE,EAAsB,KAAK,oBAAoB,EAAG,KAAK,oBAAoB,EAAG,KAAK,oBAAoB,CAAC,EAGzHF,EAAO,UAAUE,EAAsB,KAAK,SAAS,EAAG,KAAK,SAAS,EAAG,KAAK,SAAS,CAAC,EAErF,IACV,CAMD,4BAA4BG,EAASJ,EAAY,CAC7CI,EAAQ,aAAeJ,CAAU,EAAI,EACxC,CACL,CACAK,EAAW,CACPC,EAAW,CACf,EAAGE,EAAW,UAAW,cAAe,IAAI,EAE5CD,EAAc,qBAAsBC,CAAU,ECvL9C,MAAMM,EAAO,sBAKN,MAAMC,CAAW,CAIpB,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,KACf,OAAO,KAAK,OACf,CAED,WAAY,CACR,MAAMG,EAAa,KAAK,QAAQ,KAAK,WACrC,GAAIA,GAAcA,EAAW,KAAK,IAAI,EAAG,CACrC,MAAMC,EAAYD,EAAW,KAAK,IAAI,EACtC,KAAK,QAAUC,EAAU,OACzBC,EAAU,OAAO,KAAK,OAAO,CAChC,CACJ,CAID,cAAcC,EAASC,EAAMC,EAAQ,CACjC,OAAOC,EAAW,mBAAmBH,EAASC,EAAM,KAAK,KAAM,CAACG,EAAkBN,KAC9E,KAAK,QAAQ,2BAA6B,GACnC,KAAK,QAAQ,cAAcE,EAASC,EAAOI,GAAgB,CAC9D,IAAIC,EACJ,MAAMC,EAAQR,EAAU,IAAIK,EAAkB,KAAK,QAASN,EAAU,KAAK,EACrEhD,EAAOyD,EAAM,MAAQF,EAAY,KAEvC,OADA,KAAK,QAAQ,aAAa,uBAAyB,CAAC,CAAC,KAAK,QAAQ,gBAC1DE,EAAM,KAAI,CACd,IAAK,cAA6D,CAC9D,MAAMC,EAA0B,IAAIxD,EAAiBF,EAAMG,EAAQ,SAAQ,EAAI,KAAK,QAAQ,YAAY,EACxGuD,EAAwB,SAAS,OAAO,CAAC,EACzCF,EAAeE,EACf,KACH,CACD,IAAK,QAAiD,CAClDF,EAAe,IAAIlB,EAAWtC,EAAMG,EAAQ,KAAI,EAAI,KAAK,QAAQ,YAAY,EAC7E,KACH,CACD,IAAK,OAA+C,CAChD,MAAMwD,EAAmB,IAAIC,EAAU5D,EAAMG,EAAQ,KAAI,EAAIA,EAAQ,SAAU,EAAE,EAAG,EAAG,KAAK,QAAQ,YAAY,EAChHwD,EAAiB,OAAUF,EAAM,MAAQA,EAAM,KAAK,gBAAmB,KAAK,GAAK,GAAK,EACtFE,EAAiB,YAAeF,EAAM,MAAQA,EAAM,KAAK,gBAAmB,GAAK,EACjFD,EAAeG,EACf,KACH,CACD,QACI,WAAK,QAAQ,aAAa,uBAAyB,GAC7C,IAAI,MAAM,GAAGL,CAAgB,yBAAyBG,EAAM,IAAI,GAAG,CAEhF,CACDD,EAAa,iBAAmB,KAAK,QAAQ,gBAC7C,KAAK,QAAQ,aAAa,uBAAyB,GACnDC,EAAM,cAAgBD,EACtBA,EAAa,YAAc7C,EAAM,aACjC6C,EAAa,QAAUC,EAAM,MAAQI,EAAO,UAAUJ,EAAM,KAAK,EAAII,EAAO,MAAK,EACjFL,EAAa,UAAYC,EAAM,WAAa,KAAY,EAAIA,EAAM,UAClED,EAAa,MAAQC,EAAM,OAAS,KAAY,OAAO,UAAYA,EAAM,MACzED,EAAa,OAASD,EACtB,KAAK,QAAQ,eAAe,KAAKC,CAAY,EAC7CH,EAAW,mBAAmBG,EAAcF,CAAgB,EAC5DF,EAAOG,CAAW,CAClC,CAAa,EACJ,CACJ,CACL,CACAO,EAAwBlB,CAAI,EAC5BmB,EAAsBnB,EAAM,GAAOE,GAAW,IAAID,EAAWC,CAAM,CAAC", "x_google_ignoreList": [0, 1, 2]}