[{"id": "2502.18305v1", "source": "ArXiv", "title": "Exploring proteomic signatures in sepsis and non-infectious systemic inflammatory response syndrome", "abstract": "Background: The search for new biomarkers that allow an early diagnosis in sepsis has become a necessity in medicine. The objective of this study is to identify potential protein biomarkers of differential expression between sepsis and non-infectious systemic inflammatory response syndrome (NISIRS).   Methods: Prospective observational study of a cohort of septic patients activated by the Sepsis Code and patients admitted with NISIRS, during the period 2016-2017. A mass spectrometry-based approach was used to analyze the plasma proteins in the enrolled subjects. Subsequently, using recursive feature elimination (RFE) classification and cross-validation with a vector classifier, an association of these proteins in patients with sepsis compared to patients with NISIRS. The protein-protein interaction network was analyzed with String software.   Results: A total of 277 patients (141 with sepsis and 136 with NISIRS) were included. After performing RFE, 25 proteins in the study patient cohort showed statistical significance, with an accuracy of 0.960, specificity of 0.920, sensitivity of 0.973, and an AUC of 0.985. Of these, 14 proteins (vWF, PPBP, C5, C1RL, FCN3, SAA2, ORM1, ITIH3, GSN, C1QA, CA1, CFB, C3, LBP) have a greater relationship with sepsis while 11 proteins (FN1, <PERSON><PERSON><PERSON><PERSON>, <PERSON>RPINA4, APOE, APOH, C6, SERPINA3, AHSG, LUM, ITIH2, SAA1) are more expressed in NISIRS.", "authors": ["<PERSON><PERSON>", "Vicent <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> <PERSON>", "Núria Canela", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2025", "url": "http://arxiv.org/pdf/2502.18305v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"population": "septic patients activated by the Sepsis Code; sepsis compared to patients with NISIRS", "comparison": "patients with NISIRS", "outcome": "with String software"}, "results": {}, "study_design": null, "sample_size": 277, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2504.13863v1", "source": "ArXiv", "title": "Utsarjan: A smartphone App for providing kidney care and real-time assistance to children with nephrotic syndrome", "abstract": "Background Telemedicine has the potential to provide secure and cost-effective healthcare at the touch of a button. Nephrotic syndrome is a chronic childhood illness involving frequent relapses and demands long/complex treatment. Hence, developing a remote means of doctor-patient interface will ensure the provision of quality healthcare to patients. Methods The Utsarjan mobile App framework was built with Flutter that enables cross-platform development (Android, iOS, Windows) with speed, smoothness, and open-source benefits. The frontend uses Dart for user interaction, while the backend employs Node.js, Express, and NGINX for APIs, load balancing and high performance. MongoDB ensures a flexible database, Bcrypt secures passwords, PM2 handles deployment, uptime and logs, while Firebase Cloud Messaging powers free push notifications. Results Utsarjan (means excretion) is a multi-functional smartphone application for giving nephrotic care and real-time assistance to all patients (especially those in rural regions and/or who do not have access to specialists). It helps patients and doctors by ensuring opportune visits, recording each clinical test/parameter and improving medication adherence. It gives a graphical visualization of relapses, medicine dosage as well as different anthropometric parameters (urine protein, BP, height and weight). This is the first nephrotic care App that enables prompt access to doctor's advice. Conclusions Utsarjan is a mobile App to provide kidney care and real-time assistance to children with nephrotic syndrome. It gives a graphical overview of changes in a patient's health over the long course of treatment. This will assist doctors in appropriately modifying the treatment regimen. Consequently, it will (hopefully) lead to the prevention of relapses and/or complications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2025", "url": "http://arxiv.org/pdf/2504.13863v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"intervention": "regimen"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2402.05554v1", "source": "ArXiv", "title": "One-Stop Automated Diagnostic System for Carpal Tunnel Syndrome in Ultrasound Images Using Deep Learning", "abstract": "Objective: Ultrasound (US) examination has unique advantages in diagnosing carpal tunnel syndrome (CTS) while identifying the median nerve (MN) and diagnosing CTS depends heavily on the expertise of examiners. To alleviate this problem, we aimed to develop a one-stop automated CTS diagnosis system (OSA-CTSD) and evaluate its effectiveness as a computer-aided diagnostic tool. Methods: We combined real-time MN delineation, accurate biometric measurements, and explainable CTS diagnosis into a unified framework, called OSA-CTSD. We collected a total of 32,301 static images from US videos of 90 normal wrists and 40 CTS wrists for evaluation using a simplified scanning protocol. Results: The proposed model showed better segmentation and measurement performance than competing methods, reporting that HD95 score of 7.21px, ASSD score of 2.64px, Dice score of 85.78%, and IoU score of 76.00%, respectively. In the reader study, it demonstrated comparable performance with the average performance of the experienced in classifying the CTS, while outperformed that of the inexperienced radiologists in terms of classification metrics (e.g., accuracy score of 3.59% higher and F1 score of 5.85% higher). Conclusion: The OSA-CTSD demonstrated promising diagnostic performance with the advantages of real-time, automation, and clinical interpretability. The application of such a tool can not only reduce reliance on the expertise of examiners, but also can help to promote the future standardization of the CTS diagnosis process, benefiting both patients and radiologists.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Zhenzhou Li"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2402.05554v1", "doi": "10.1016/j.ultrasmedbio.2023.10.009", "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"comparison": "ization of the CTS diagnosis process"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "38996123", "source": "PubMed", "title": "Combined traditional Chinese medicine therapy for the treatment of infertility with polycystic ovary syndrome: A network meta-analysis of randomized controlled trials.", "abstract": "Polycystic ovary syndrome (PCOS) infertility has attracted great attention from researchers due to its high incidence. Numerous studies have shown that Chinese medicine is effective in treating this disease, but there is a wide variety of Chinese medicine therapies available, and there is a lack of comparative evaluation of the efficacy of various Chinese medicine combination therapies in the clinic, which requires further in-depth exploration. This study aims to evaluate the efficacy of a combined traditional Chinese medicine (TCM) therapy for the treatment of infertility with PCOS using network meta-analysis (NMA). In PubMed, web of Science, Cochrane Library, Embase, China Knowledge Network, Wanfang Data, VIP Database, China Biomedical Literature Database (SinoMed) databases, searchs were conducted for information about the randomized controlled trials (RCTs) of combined TCM therapy for the treatment of infertility with PCOS. Quality evaluation was performed using the Cochrane 5.3 risk of bias assessment tool, and NMA using Stata 16.0. This study comprised 28 RCTs using 8 combined TCM therapies in total. The results of the NMA showed that moxibustion + herbal, fire acupuncture + herbal, acupuncture + herbal, electroacupuncture + herbal, and acupoint application + herbal improved the clinical pregnancy rate better than acupuncture, herbal, and western medicines monotherapy (P < .05). Additionally, ear point pressure + herbal enema + herbal, acupuncture and moxibustion + herbal, fire acupuncture + herbal, and acupuncture + herbal improved the ovulation rate better than acupuncture, herbal, and western medicines monotherapy (P < .05). Moxibustion + herbal, fire acupuncture + herbal, and acupuncture + herbal are the 3 most effective therapies for improving the clinical pregnancy rate. Fire acupuncture + herbal, acupuncture + herbal, and ear point pressure + herbal enema + herbal are the 3 most effective therapies for improving the ovulation rate. The combined TCM therapy demonstrated better efficacy for the treatment of infertility with PCOS compared to acupuncture, herbal, and western medicines monotherapy. However, the optimal treatment therapy varied depending on the outcome indicators. Further large sample, high-quality, and standardized RCTs are needed to verify these findings.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "journal": "Medicine", "year": "2024", "url": "https://pubmed.ncbi.nlm.nih.gov/38996123/", "doi": "", "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"intervention": "of infertility with PCOS using network meta-analysis; of infertility with PCOS; of infertility with PCOS compared to acupuncture; therapy varied depending on the outcome indicators", "comparison": "led trials; acupuncture; ized RCTs are needed to verify these findings"}, "results": {}, "study_design": "rct", "sample_size": null, "quality_indicators": {"randomized": true, "blinded": null, "controlled": null}}}, {"id": "2302.05450v1", "source": "ArXiv", "title": "A network-based biomarkers discovery of Cold/Hot ZHENG chronic gastritis and Cold/Hot herbs of formulae", "abstract": "Objective: To discover biomarkers and uncover the mechanism of Cold/Hot ZHENG (syndrome in traditional Chinese medicine) chronic gastritis (CG) and Cold/Hot herbs in traditional Chinese medicine (TCM) formulae on systematic biology. Background: CG is a common inflammatory disease and the diagnosis of CG in TCM can be classified into Cold ZHENG (Asthenic Cold) and Hot ZHENG (Excess Hot). However, the molecular features of Cold/Hot ZHENG in CG and the mechanism of Cold/Hot herbs in formulae for CG remained unclear. Methods: Based on data of 35 patients of Cold/Hot ZHENG CG and 3 scRNA-seq CG samples, we conduct analysis with transcriptomics datasets and algorithms, to discover biomarkers for Cold/Hot ZHENG CG. And we collected 25 formulae (with traditional effects related to Cold/Hot ZHENG) for CG and corresponding 89 Cold/Hot herbs (including Warm/Cool herbs) to discover features and construct target networks of Cold/Hot herbs on the basis of network target and enrichment analysis. Results: Biomarkers of Cold/Hot ZHENG CG represented by CCL2 and LEP suggested that Hot ZHENG CG might be characterized by over-inflammation and exuberant metabolism, and Cold ZHENG CG showed a trend of suppression in immune regulation and energy metabolism. And biomarkers of Cold/Hot ZHENG showed also significant changes in the progression of gastric cancer. And biomarkers and pathways of Hot herbs intend to regulate immune responses and energy metabolism, while those of Cold herbs were likely to participate in anti-inflammation effect. Conclusion: In this study, we found that the biomarkers and mechanism of Cold/Hot ZHENG CG and those of Cold/Hot herbs were closely related to the regulation of immune and metabolisms. These findings may reflect the mechanism, build bridges between multiple views of Cold/Hot ZHENG and Cold/Hot herbs, and provide a research paradigm for further achieving precision TCM.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2023", "url": "http://arxiv.org/pdf/2302.05450v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"population": "Cold/Hot ZHENG CG", "intervention": "/Cool herbs"}, "results": {}, "study_design": null, "sample_size": 35, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2309.02959v3", "source": "ArXiv", "title": "A Non-Invasive Interpretable NAFLD Diagnostic Method Combining TCM Tongue Features", "abstract": "Non-alcoholic fatty liver disease (NAFLD) is a clinicopathological syndrome characterized by hepatic steatosis resulting from the exclusion of alcohol and other identifiable liver-damaging factors. It has emerged as a leading cause of chronic liver disease worldwide. Currently, the conventional methods for NAFLD detection are expensive and not suitable for users to perform daily diagnostics. To address this issue, this study proposes a non-invasive and interpretable NAFLD diagnostic method, the required user-provided indicators are only Gender, Age, Height, Weight, Waist Circumference, Hip Circumference, and tongue image. This method involves merging patients' physiological indicators with tongue features, which are then input into a fusion network named SelectorNet. SelectorNet combines attention mechanisms with feature selection mechanisms, enabling it to autonomously learn the ability to select important features. The experimental results show that the proposed method achieves an accuracy of 77.22\\% using only non-invasive data, and it also provides compelling interpretability matrices. This study contributes to the early diagnosis of NAFLD and the intelligent advancement of TCM tongue diagnosis. The project mentioned in this paper is currently publicly available.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Qingfeng Wu", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2023", "url": "http://arxiv.org/pdf/2309.02959v3", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2110.08331v1", "source": "ArXiv", "title": "A New Approach for Interpretability and Reliability in Clinical Risk Prediction: Acute Coronary Syndrome Scenario", "abstract": "We intend to create a new risk assessment methodology that combines the best characteristics of both risk score and machine learning models. More specifically, we aim to develop a method that, besides having a good performance, offers a personalized model and outcome for each patient, presents high interpretability, and incorporates an estimation of the prediction reliability which is not usually available. By combining these features in the same approach we expect that it can boost the confidence of physicians to use such a tool in their daily activity. In order to achieve the mentioned goals, a three-step methodology was developed: several rules were created by dichotomizing risk factors; such rules were trained with a machine learning classifier to predict the acceptance degree of each rule (the probability that the rule is correct) for each patient; that information was combined and used to compute the risk of mortality and the reliability of such prediction. The methodology was applied to a dataset of patients admitted with any type of acute coronary syndromes (ACS), to assess the 30-days all-cause mortality risk. The performance was compared with state-of-the-art approaches: logistic regression (LR), artificial neural network (ANN), and clinical risk score model (Global Registry of Acute Coronary Events - GRACE). The proposed approach achieved testing results identical to the standard LR, but offers superior interpretability and personalization; it also significantly outperforms the GRACE risk model and the standard ANN model. The calibration curve also suggests a very good generalization ability of the obtained model as it approaches the ideal curve. Finally, the reliability estimation of individual predictions presented a great correlation with the misclassifications rate. Those properties may have a beneficial application in other clinical scenarios as well. [abridged]", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> de Carvalho", "<PERSON>"], "journal": "ArXiv", "year": "2021", "url": "http://arxiv.org/pdf/2110.08331v1", "doi": "10.1016/j.artmed.2021.102113", "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"comparison": "state-of-the-art approaches: logistic regression; LR; ANN model"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2104.15106v3", "source": "ArXiv", "title": "Latent Factor Decomposition Model: Applications for Questionnaire Data", "abstract": "The analysis of clinical questionnaire data comes with many inherent challenges. These challenges include the handling of data with missing fields, as well as the overall interpretation of a dataset with many fields of different scales and forms. While numerous methods have been developed to address these challenges, they are often not robust, statistically sound, or easily interpretable. Here, we propose a latent factor modeling framework that extends the principal component analysis for both categorical and quantitative data with missing elements. The model simultaneously provides the principal components (basis) and each patients' projections on these bases in a latent space. We show an application of our modeling framework through Irritable Bowel Syndrome (IBS) symptoms, where we find correlations between these projections and other standardized patient symptom scales. This latent factor model can be easily applied to different clinical questionnaire datasets for clustering analysis and interpretable inference.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2021", "url": "http://arxiv.org/pdf/2104.15106v3", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"comparison": "ized patient symptom scales"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "35027910", "source": "PubMed", "title": "Kuntai Capsule Combined With Letrozole on Gonadal Hormone Levels and Ovarian Function in Patients With PCOS: A Systematic Review and Meta-Analysis.", "abstract": "The efficacy of Kuntai capsule combined with letrozole (LE) in improving ovarian function of polycystic ovary syndrome (PCOS) has been evaluated before, but there is still a lack of evidence-based support for the regulation of sex hormone levels. In recent years, new randomized clinical trials (RCTs) have been reported on the effect of combined therapy on regulating sex hormone levels. We aimed to systematically evaluate the efficacy of Kuntai capsule combined with LE in the treatment of PCOS. A search across the China Biomedical Literature Database (CBM), China National Knowledge Infrastructure (CNKI), China Science and Technology Journal Database (VIP), Wanfang database, PubMed, Web of Science, The Cochrane Library, and Embase was conducted on Kuntai capsule combined with LE in the treatment of PCOS. The time of the self-built database was up to April 30, 2021. RCTs of LE in the control group and LE combined with Kuntai capsule in the experimental group were selected. RevMan5.3 software was used for data analysis. A total of 17 studies were gathered, which included 1,684 patients. The meta-analysis results showed that the total effective rate of the combined group was 93.36% and that of the LE group was 78.15%. The improvement in the ovulation rate, pregnancy rate, number of mature follicles, endometrial thickness, cervical mucus score, and serum follicle stimulating hormone (FSH), luteinizing hormone (LH), and prolactin (PRL) in the combined group was consistent with the results of a previous meta-analysis and was better than that in the LE group (<i>p</i> < 0.05). In addition, the combination group was better than the LE group in regulating the levels of estradiol (E2) and testosterone (T) (<i>p</i> < 0.05). There were no adverse drug reactions in the two groups during treatment. As a type of pure traditional Chinese medicine preparation, Kuntai capsule combined with LE had a better effect than LE alone in the treatment of PCOS, with advantages mainly reflected in enhancing ovarian function and regulating the levels of sex hormones <i>in vivo</i>, among others, but the value of combined therapy still needs to be verified by more high-quality RCTs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON>", "Shoujin Dong", "Congcong Yu"], "journal": "Frontiers in endocrinology", "year": "2021", "url": "https://pubmed.ncbi.nlm.nih.gov/35027910/", "doi": "", "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"intervention": "of PCOS; of PCOS; of PCOS", "comparison": "and LE combined with Ku<PERSON>i capsule in the experimental group were selected", "outcome": "before"}, "results": {}, "study_design": "rct", "sample_size": 1684, "quality_indicators": {"randomized": true, "blinded": null, "controlled": true}}}, {"id": "34743745", "source": "PubMed", "title": "Efficacy of Bushen Huatan Decoction combined with Baduanjin in the treatment of polycystic ovary syndrome with insulin resistance (IR-PCOS), kidney deficiency and phlegm dampness: study protocol for a randomized controlled trial.", "abstract": "Polycystic ovary syndrome (PCOS) is a common reproductive endocrine disease in women. Insulin resistance (IR) has emerged as a central contributor to the pathogenesis of this disease. According to traditional Chinese medicine (TCM), kidney deficiency is the main syndrome of PCOS. The deficiency of the kidney cannot vaporize water-dampness, and the retention of water-dampness accumulates into phlegm dampness stagnation, resulting in visceral dysfunction and metabolic disorder. TCM involving syndrome differentiation and treatment is widely used to adjust women's menstrual cycles. Our patented formula Bushen Huatan Decoction (BSHTD) has been proven to be effective in the clinical treatment of IR-PCOS. Baduanjin also plays an important role in improving metabolic syndrome through lifestyle intervention. This study investigates the clinical efficacy of Bushen Huatan Decoction combined with Baduanjin in IR-PCOS, to form a specific TCM-behaviour intervention plan in the treatment of IR-PCOS. This is a randomized controlled trial involving 190 participants diagnosed with IR-PCOS. All participants will be randomly allocated into 5 groups: group A will receive metformin; group B, BSHTD; group C, Baduanjin; group D, BSHTD combined with metformin; and group E, BSHTD combined with Baduanjin. One course of treatment lasts 3 months, a total of two courses. The primary outcomes are changes in the homeostatic model assessment of insulin resistance (HOMA-IR) and improvements in the oral glucose tolerance test (OGTT) and insulin-releasing test (INS). The secondary outcomes are improvements in the menstrual cycle, ovulation rate, clinical pregnancy rate, basic serum sex hormone levels, free androgen index (FAI), Ferriman-Gallwey scores, body mass index (BMI) and TCM syndrome scores. The related observation indexes will be collected at baseline, during the process of treatment and at the 6-month follow-up. Simultaneously, close monitoring of possible adverse events will be performed throughout the trial process. This trial will investigate the efficacy of the comprehensive intervention program of Bushen Huatan Decoction combined with Baduanjin on the adjustment of the menstrual cycle, improvement of insulin resistance and correction of glucose metabolism disorder in IR-PCOS patients. It is expected to form an alternative treatment of TCM-behaviour intervention therapy for IR-PCOS and promote the Chinese fitness Qigong Baduanjin in the application of lifestyle diseases. Chinese Clinical Trial Registry ChiCTR2100043415 . Registered on 15 February 2021.", "authors": ["Haiqing Qian", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "Trials", "year": "2021", "url": "https://pubmed.ncbi.nlm.nih.gov/34743745/", "doi": "", "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"population": "IR-PCOS", "intervention": "is widely used to adjust women's menstrual cycles; of IR-PCOS; plan in the treatment of IR-PCOS; lasts 3 months; and at the 6-month follow-up; program of Bushen Huatan Decoction combined with Baduanjin on the adjustment of the menstrual cycle; of TCM-behaviour intervention therapy for IR-PCOS", "comparison": "led trial involving 190 participants diagnosed with IR-PCOS", "outcome": "s are changes in the homeostatic model assessment of insulin resistance; s are improvements in the menstrual cycle"}, "results": {}, "study_design": "rct", "sample_size": 190, "quality_indicators": {"randomized": true, "blinded": null, "controlled": null}}}, {"id": "2004.04980v1", "source": "ArXiv", "title": "Negation Detection for Clinical Text Mining in Russian", "abstract": "Developing predictive modeling in medicine requires additional features from unstructured clinical texts. In Russia, there are no instruments for natural language processing to cope with problems of medical records. This paper is devoted to a module of negation detection. The corpus-free machine learning method is based on gradient boosting classifier is used to detect whether a disease is denied, not mentioned or presented in the text. The detector classifies negations for five diseases and shows average F-score from 0.81 to 0.93. The benefits of negation detection have been demonstrated by predicting the presence of surgery for patients with the acute coronary syndrome.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2004.04980v1", "doi": "10.3233/SHTI200179", "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"population": "the acute coronary syndrome"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2505.13156v1", "source": "ArXiv", "title": "Tianyi: A Traditional Chinese Medicine all-rounder language model and its Real-World Clinical Practice", "abstract": "Natural medicines, particularly Traditional Chinese Medicine (TCM), are gaining global recognition for their therapeutic potential in addressing human symptoms and diseases. TCM, with its systematic theories and extensive practical experience, provides abundant resources for healthcare. However, the effective application of TCM requires precise syndrome diagnosis, determination of treatment principles, and prescription formulation, which demand decades of clinical expertise. Despite advancements in TCM-based decision systems, machine learning, and deep learning research, limitations in data and single-objective constraints hinder their practical application. In recent years, large language models (LLMs) have demonstrated potential in complex tasks, but lack specialization in TCM and face significant challenges, such as too big model scale to deploy and issues with hallucination. To address these challenges, we introduce <PERSON><PERSON><PERSON> with 7.6-billion-parameter LLM, a model scale proper and specifically designed for TCM, pre-trained and fine-tuned on diverse TCM corpora, including classical texts, expert treatises, clinical records, and knowledge graphs. Tianyi is designed to assimilate interconnected and systematic TCM knowledge through a progressive learning manner. Additionally, we establish TCMEval, a comprehensive evaluation benchmark, to assess LLMs in TCM examinations, clinical tasks, domain-specific question-answering, and real-world trials. The extensive evaluations demonstrate the significant potential of <PERSON><PERSON><PERSON> as an AI assistant in TCM clinical practice and research, bridging the gap between TCM knowledge and practical application.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2025", "url": "http://arxiv.org/pdf/2505.13156v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "principles"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2502.04345v2", "source": "ArXiv", "title": "JingFang: An Expert-Level Large Language Model for Traditional Chinese Medicine Clinical Consultation and Syndrome Differentiation-Based Treatment", "abstract": "The effective application of traditional Chinese medicine (TCM) requires extensive knowledge of TCM and clinical experience. The emergence of Large Language Models (LLMs) provides a solution to this, while existing LLMs for TCM exhibit critical limitations of incomplete clinical consultation and diagnoses, as well as inaccurate syndrome differentiation. To address these issues, we establish JingFang (JF), a novel TCM LLM that demonstrates the level of expertise in clinical consultation and syndrome differentiation. We propose a Multi-Agent Collaborative Chain-of-Thought Mechanism (MACCTM) for comprehensive and targeted clinical consultation, enabling JF with effective and accurate diagnostic ability. In addition, a Syndrome Agent and a Dual-Stage Recovery Scheme (DSRS) are developed to accurately enhance the differentiation of the syndrome and the subsequent corresponding treatment. JingFang not only facilitates the application of LLMs but also promotes the effective application of TCM for healthcare.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "R<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "Guodong Shan", "Chisheng Li"], "journal": "ArXiv", "year": "2025", "url": "http://arxiv.org/pdf/2502.04345v2", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2506.12107v1", "source": "ArXiv", "title": "Network Pharmacology Reveals HSPA1A/BST2 as Potential Targets of Ci Bai Capsule's Active Compounds Intervening in Leukopenia", "abstract": "Background: Radiation-induced leukopenia caused by low-dose exposure is frequently associated with Traditional Chinese Medicine (TCM) syndromes like \"blood deficiency\" and \"fatigue syndrome\". <PERSON><PERSON> (CB) has been reported to enhance white blood cell levels; however, its mechanisms and bioactive compounds remain unclear.Aim: This study aimed to identify the bioactive compounds group of CB and elucidate its potential mechanisms in radiation-induced leukopenia.Methods: Syndrome-related data were gathered from SYMMAP and CTD database. CB's target profile is predicted by DrugCIPHER. Network pharmacology approaches were employed to identify active compounds and related pathways. Experimental validation was conducted through flow cytometry and RNA-sequencing in both ex vivo and in vivo models.Results: A total of 22 pathways related to cellular processes, immune responses, and signal transduction were identified. Five key bioactive compounds (kaempferol-3-glucorhamnoside, syringin, schisandrin, 3-hydroxytyrosol 3-O-glucoside and salidroside) were found to significantly modulate syndrome-related pathways. Optimal dosing of this compound combination enhanced leukocyte counts and splenic immune cell proliferation in irradiated mice. Transcriptomic analysis revealed that the compounds exert regulatory effects on PP1A, RB, CDK4/6, CDK2, and CDK1, thereby modulating downstream immune and hematopoietic markers such as MNDA, BST2, and HSPA1A.Conclusion: Our findings suggest that CB mitigates radiation-induced leukopenia by enhancing immune and hematopoietic recovery, offering a promising therapeutic approach for managing radiation-related hematological disorders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Shen", "<PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2025", "url": "http://arxiv.org/pdf/2506.12107v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "cology approaches were employed to identify active compounds"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2503.20333v1", "source": "ArXiv", "title": "Phase-Center-Constrained Beamforming for Minimizing Phase-Center Displacement", "abstract": "Accurate knowledge and control of the phase center in antenna arrays is essential for high-precision applications such as Global Navigation Satellite Systems (GNSS), where even small displacements can introduce significant localization errors. Traditional beamforming techniques applied to array antennas often neglect the variation of the phase center, resulting in unwanted spatial shifts, and in consequence, localization errors. In this work, we propose a novel beamforming algorithm, called Phase-Center-Constrained Beamforming (PCCB), which explicitly minimizes the displacement of the phase center (Phase Center Offset, PCO) while preserving a chosen directional gain. We formulate the problem as a constrained optimization problem and incorporate regularization terms that enforce energy compactness and beampattern fidelity. The resulting PCCB approach allows for directional gain control and interference nulling while significantly reducing PCO displacement. Experimental validation using a simulated GNSS antenna array demonstrates that our PCCB approach achieves a fivefold reduction in PCO shift compared to the PCO shifts obtained when using conventional beamforming. A stability analysis across multiple random initializations confirms the robustness of our method and highlights the benefit of repeated optimization. These results indicate that our PCCB approach can serve as a practical and effective solution for decreasing phase center variability.", "authors": ["<PERSON>", "Noori BniLam"], "journal": "ArXiv", "year": "2025", "url": "http://arxiv.org/pdf/2503.20333v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"comparison": "of the phase center in antenna arrays is essential for high-precision applications such as Global Navigation Satellite Systems; and interference nulling while significantly reducing PCO displacement; the PCO shifts obtained when using conventional beamforming"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": true}}}, {"id": "2503.10271v2", "source": "ArXiv", "title": "Unveiling Sleep Dysregulation in Chronic Fatigue Syndrome with and without Fibromyalgia Through Bayesian Networks", "abstract": "Chronic Fatigue Syndrome (CFS) and Fibromyalgia (FM) often co-occur as medically unexplained conditions linked to disrupted physiological regulation, including altered sleep. Building on the work of <PERSON><PERSON> et al. (2011), who identified differences in sleep-stage transitions in women with CFS and CFS+FM, we exploited the same strictly controlled clinical cohort using a Bayesian Network (BN) to quantify detailed patterns of sleep and its dynamics. Our BN confirmed that sleep transitions are best described as a second-order process (<PERSON><PERSON> et al., 2018), achieving a next-stage predictive accuracy of 70.6%, validated on two independent data sets with domain shifts (60.1-69.8% accuracy). Notably, we demonstrated that sleep dynamics can reveal the actual diagnoses. Our BN successfully differentiated healthy, CFS, and CFS+FM individuals, achieving an AUROC of 75.4%. Using interventions, we quantified sleep alterations attributable specifically to CFS and CFS+FM, identifying changes in stage prevalence, durations, and first- and second-order transitions. These findings reveal novel markers for CFS and CFS+FM in early-to-mid-adulthood women, offering insights into their physiological mechanisms and supporting their clinical differentiation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2025", "url": "http://arxiv.org/pdf/2503.10271v2", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "s", "comparison": "led clinical cohort using a Bayesian Network"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2411.11027v1", "source": "ArXiv", "title": "BianCang: A Traditional Chinese Medicine Large Language Model", "abstract": "The rise of large language models (LLMs) has driven significant progress in medical applications, including traditional Chinese medicine (TCM). However, current medical LLMs struggle with TCM diagnosis and syndrome differentiation due to substantial differences between TCM and modern medical theory, and the scarcity of specialized, high-quality corpora. This paper addresses these challenges by proposing BianCang, a TCM-specific LLM, using a two-stage training process that first injects domain-specific knowledge and then aligns it through targeted stimulation. To enhance diagnostic and differentiation capabilities, we constructed pre-training corpora, instruction-aligned datasets based on real hospital records, and the ChP-TCM dataset derived from the Pharmacopoeia of the People's Republic of China. We compiled extensive TCM and medical corpora for continuous pre-training and supervised fine-tuning, building a comprehensive dataset to refine the model's understanding of TCM. Evaluations across 11 test sets involving 29 models and 4 tasks demonstrate the effectiveness of BianCang, offering valuable insights for future research. Code, datasets, and models are available at https://github.com/QLU-NLP/BianCang.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>peng Lu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2411.11027v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "copoeia of the People's Republic of China"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2410.19451v1", "source": "ArXiv", "title": "Intelligent Understanding of Large Language Models in Traditional Chinese Medicine Based on Prompt Engineering Framework", "abstract": "This paper explores the application of prompt engineering to enhance the performance of large language models (LLMs) in the domain of Traditional Chinese Medicine (TCM). We propose TCM-Prompt, a framework that integrates various pre-trained language models (PLMs), templates, tokenization, and verbalization methods, allowing researchers to easily construct and fine-tune models for specific TCM-related tasks. We conducted experiments on disease classification, syndrome identification, herbal medicine recommendation, and general NLP tasks, demonstrating the effectiveness and superiority of our approach compared to baseline methods. Our findings suggest that prompt engineering is a promising technique for improving the performance of LLMs in specialized domains like TCM, with potential applications in digitalization, modernization, and personalized medicine.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2410.19451v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"comparison": "baseline methods"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2411.15491v1", "source": "ArXiv", "title": "Traditional Chinese Medicine Case Analysis System for High-Level Semantic Abstraction: Optimized with Prompt and RAG", "abstract": "This paper details a technical plan for building a clinical case database for Traditional Chinese Medicine (TCM) using web scraping. Leveraging multiple platforms, including 360doc, we gathered over 5,000 TCM clinical cases, performed data cleaning, and structured the dataset with crucial fields such as patient details, pathogenesis, syndromes, and annotations. Using the $Baidu\\_ERNIE\\_Speed\\_128K$ API, we removed redundant information and generated the final answers through the $DeepSeekv2$ API, outputting results in standard JSON format. We optimized data recall with RAG and rerank techniques during retrieval and developed a hybrid matching scheme. By combining two-stage retrieval method with keyword matching via Jieba, we significantly enhanced the accuracy of model outputs.", "authors": ["<PERSON><PERSON>", "Hongjin Wu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2411.15491v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"comparison": "JSON format"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2402.02326v1", "source": "ArXiv", "title": "Watt-level all polarization-maintaining femtosecond fiber laser source at 1100 nm for multicolor two-photon fluorescence excitation of fluorescent proteins", "abstract": "We demonstrate a compact watt-level all polarization-maintaining (PM) femtosecond fiber laser source at 1100 nm. The fiber laser source is seeded by an all PM fiber mode-locked laser employing a nonlinear amplifying loop mirror. The seed laser can generate stable pulses at a fundamental repetition rate of 40.71 MHz with a signal-to-noise rate of >100 dB and an integrated relative intensity noise of only ~0.061%. After two-stage external amplification and pulse compression, an output power of ~1.47 W (corresponding to a pulse energy of ~36.1 nJ) and a pulse duration of ~251 fs are obtained. The 1100 nm femtosecond fiber laser is then employed as the excitation light source for multicolor multi-photon fluorescence microscopy of Chinese hamster ovary (CHO) cells stably expressing red fluorescent proteins.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2402.02326v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2303.14401v1", "source": "ArXiv", "title": "Deep Linear Discriminant Analysis with Variation for Polycystic Ovary Syndrome Classification", "abstract": "The polycystic ovary syndrome diagnosis is a problem that can be leveraged using prognostication based learning procedures. Many implementations of PCOS can be seen with Machine Learning but the algorithms have certain limitations in utilizing the processing power graphical processing units. The simple machine learning algorithms can be improved with advanced frameworks using Deep Learning. The Linear Discriminant Analysis is a linear dimensionality reduction algorithm for classification that can be boosted in terms of performance using deep learning with Deep LDA, a transformed version of the traditional LDA. In this result oriented paper we present the Deep LDA implementation with a variation for prognostication of PCOS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2023", "url": "http://arxiv.org/pdf/2303.14401v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2305.03257v1", "source": "ArXiv", "title": "Data-driven and Physics Informed Modelling of Chinese Hamster Ovary Cell Bioreactors", "abstract": "Fed-batch culture is an established operation mode for the production of biologics using mammalian cell cultures. Quantitative modeling integrates both kinetics for some key reaction steps and optimization-driven metabolic flux allocation, using flux balance analysis; this is known to lead to certain mathematical inconsistencies. Here, we propose a physically-informed data-driven hybrid model (a \"gray box\") to learn models of the dynamical evolution of Chinese Hamster Ovary (CHO) cell bioreactors from process data. The approach incorporates physical laws (e.g. mass balances) as well as kinetic expressions for metabolic fluxes. Machine learning (ML) is then used to (a) directly learn evolution equations (black-box modelling); (b) recover unknown physical parameters (\"white-box\" parameter fitting) or -- importantly -- (c) learn partially unknown kinetic expressions (gray-box modelling). We encode the convex optimization step of the overdetermined metabolic biophysical system as a differentiable, feed-forward layer into our architectures, connecting partial physical knowledge with data-driven machine learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2023", "url": "http://arxiv.org/pdf/2305.03257v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2311.01328v2", "source": "ArXiv", "title": "Analog information decoding of bosonic quantum LDPC codes", "abstract": "Quantum error correction is crucial for scalable quantum information processing applications. Traditional discrete-variable quantum codes that use multiple two-level systems to encode logical information can be hardware-intensive. An alternative approach is provided by bosonic codes, which use the infinite-dimensional Hilbert space of harmonic oscillators to encode quantum information. Two promising features of bosonic codes are that syndrome measurements are natively analog and that they can be concatenated with discrete-variable codes. In this work, we propose novel decoding methods that explicitly exploit the analog syndrome information obtained from the bosonic qubit readout in a concatenated architecture. Our methods are versatile and can be generally applied to any bosonic code concatenated with a quantum low-density parity-check (QLDPC) code. Furthermore, we introduce the concept of quasi-single-shot protocols as a novel approach that significantly reduces the number of repeated syndrome measurements required when decoding under phenomenological noise. To realize the protocol, we present a first implementation of time-domain decoding with the overlapping window method for general QLDPC codes, and a novel analog single-shot decoding method. Our results lay the foundation for general decoding algorithms using analog information and demonstrate promising results in the direction of fault-tolerant quantum computation with concatenated bosonic-QLDPC codes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Josch<PERSON> Roffe"], "journal": "ArXiv", "year": "2023", "url": "http://arxiv.org/pdf/2311.01328v2", "doi": "10.1103/PRXQuantum.5.020349", "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "nic oscillators to encode quantum information"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2203.10839v2", "source": "ArXiv", "title": "TCM-SD: A Benchmark for Probing Syndrome Differentiation via Natural Language Processing", "abstract": "Traditional Chinese Medicine (TCM) is a natural, safe, and effective therapy that has spread and been applied worldwide. The unique TCM diagnosis and treatment system requires a comprehensive analysis of a patient's symptoms hidden in the clinical record written in free text. Prior studies have shown that this system can be informationized and intelligentized with the aid of artificial intelligence (AI) technology, such as natural language processing (NLP). However, existing datasets are not of sufficient quality nor quantity to support the further development of data-driven AI technology in TCM. Therefore, in this paper, we focus on the core task of the TCM diagnosis and treatment system -- syndrome differentiation (SD) -- and we introduce the first public large-scale dataset for SD, called TCM-SD. Our dataset contains 54,152 real-world clinical records covering 148 syndromes. Furthermore, we collect a large-scale unlabelled textual corpus in the field of TCM and propose a domain-specific pre-trained language model, called ZY-BERT. We conducted experiments using deep neural networks to establish a strong performance baseline, reveal various challenges in SD, and prove the potential of domain-specific pre-trained language model. Our study and analysis reveal opportunities for incorporating computer science and linguistics knowledge to explore the empirical validity of TCM theories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2022", "url": "http://arxiv.org/pdf/2203.10839v2", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "system requires a comprehensive analysis of a patient's symptoms hidden in the clinical record written in free text; system -- syndrome differentiation"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2208.05601v4", "source": "ArXiv", "title": "Adaptive syndrome measurements for Shor-style error correction", "abstract": "The Shor fault-tolerant error correction (FTEC) scheme uses transversal gates and ancilla qubits prepared in the cat state in syndrome extraction circuits to prevent propagation of errors caused by gate faults. For a stabilizer code of distance $d$ that can correct up to $t=\\lfloor(d-1)/2\\rfloor$ errors, the traditional Shor scheme handles ancilla preparation and measurement faults by performing syndrome measurements until the syndromes are repeated $t+1$ times in a row; in the worst-case scenario, $(t+1)^2$ rounds of measurements are required. In this work, we improve the Shor FTEC scheme using an adaptive syndrome measurement technique. The syndrome for error correction is determined based on information from the differences of syndromes obtained from consecutive rounds. Our protocols that satisfy the strong and the weak FTEC conditions require no more than $(t+3)^2/4-1$ rounds and $(t+3)^2/4-2$ rounds, respectively, and are applicable to any stabilizer code. Our simulations of FTEC protocols with the adaptive schemes on hexagonal color codes of small distances verify that our protocols preserve the code distance, can increase the pseudothreshold, and can decrease the average number of rounds compared to the traditional Shor scheme. We also find that for the code of distance $d$, our FTEC protocols with the adaptive schemes require no more than $d$ rounds on average.", "authors": ["Theerapat Tansuwannont", "<PERSON><PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2022", "url": "http://arxiv.org/pdf/2208.05601v4", "doi": "10.22331/q-2023-08-08-1075", "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"comparison": "the traditional Shor scheme"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2210.08411v5", "source": "ArXiv", "title": "Error-correcting codes for fermionic quantum simulation", "abstract": "Utilizing the framework of $\\mathbb{Z}_2$ lattice gauge theories in the context of Pauli stabilizer codes, we present methodologies for simulating fermions via qubit systems on a two-dimensional square lattice. We investigate the symplectic automorphisms of the <PERSON><PERSON> module over the <PERSON> polynomial ring. This enables us to systematically increase the code distances of stabilizer codes while fixing the rate between encoded logical fermions and physical qubits. We identify a family of stabilizer codes suitable for fermion simulation, achieving code distances of $d=2,3,4,5,6,7$, allowing correction of any $\\lfloor \\frac{d-1}{2} \\rfloor$-qubit error. In contrast to the traditional code concatenation approach, our method can increase the code distances without decreasing the (fermionic) code rate. In particular, we explicitly show all stabilizers and logical operators for codes with code distances of $d=3,4,5$. We provide syndromes for all Pauli errors and invent a syndrome-matching algorithm to compute code distances numerically.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>ey <PERSON>", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2022", "url": "http://arxiv.org/pdf/2210.08411v5", "doi": "10.21468/SciPostPhys.16.1.033", "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2110.10378v2", "source": "ArXiv", "title": "Machine Learning for Continuous Quantum Error Correction on Superconducting Qubits", "abstract": "Continuous quantum error correction has been found to have certain advantages over discrete quantum error correction, such as a reduction in hardware resources and the elimination of error mechanisms introduced by having entangling gates and ancilla qubits. We propose a machine learning algorithm for continuous quantum error correction that is based on the use of a recurrent neural network to identify bit-flip errors from continuous noisy syndrome measurements. The algorithm is designed to operate on measurement signals deviating from the ideal behavior in which the mean value corresponds to a code syndrome value and the measurement has white noise. We analyze continuous measurements taken from a superconducting architecture using three transmon qubits to identify three significant practical examples of non-ideal behavior, namely auto-correlation at temporal short lags, transient syndrome dynamics after each bit-flip, and drift in the steady-state syndrome values over the course of many experiments. Based on these real-world imperfections, we generate synthetic measurement signals from which to train the recurrent neural network, and then test its proficiency when implementing active error correction, comparing this with a traditional double threshold scheme and a discrete Bayesian classifier. The results show that our machine learning protocol is able to outperform the double threshold protocol across all tests, achieving a final state fidelity comparable to the discrete Bayesian classifier.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2021", "url": "http://arxiv.org/pdf/2110.10378v2", "doi": "10.1088/1367-2630/ac66f9", "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2109.12323v1", "source": "ArXiv", "title": "Deep Learning-Based Detection of the Acute Respiratory Distress Syndrome: What Are the Models Learning?", "abstract": "The acute respiratory distress syndrome (ARDS) is a severe form of hypoxemic respiratory failure with in-hospital mortality of 35-46%. High mortality is thought to be related in part to challenges in making a prompt diagnosis, which may in turn delay implementation of evidence-based therapies. A deep neural network (DNN) algorithm utilizing unbiased ventilator waveform data (VWD) may help to improve screening for ARDS. We first show that a convolutional neural network-based ARDS detection model can outperform prior work with random forest models in AUC (0.95+/-0.019 vs. 0.88+/-0.064), accuracy (0.84+/-0.026 vs 0.80+/-0.078), and specificity (0.81+/-0.06 vs 0.71+/-0.089). Frequency ablation studies imply that our model can learn features from low frequency domains typically used for expert feature engineering, and high-frequency information that may be difficult to manually featurize. Further experiments suggest that subtle, high-frequency components of physiologic signals may explain the superior performance of DL models over traditional ML when using physiologic waveform data. Our observations may enable improved interpretability of DL-based physiologic models and may improve the understanding of how high-frequency information in physiologic data impacts the performance our DL model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2021", "url": "http://arxiv.org/pdf/2109.12323v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"comparison": "0; 0; 0"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2007.13926v1", "source": "ArXiv", "title": "Intelligent Optimization of Diversified Community Prevention of COVID-19 using Traditional Chinese Medicine", "abstract": "Traditional Chinese medicine (TCM) has played an important role in the prevention and control of the novel coronavirus pneumonia (COVID-19), and community prevention has become the most essential part in reducing the spread risk and protecting populations. However, most communities use a uniform TCM prevention program for all residents, which violates the \"treatment based on syndrome differentiation\" principle of TCM and limits the effectiveness of prevention. In this paper, we propose an intelligent optimization method to develop diversified TCM prevention programs for community residents. First, we use a fuzzy clustering method to divide the population based on both modern medicine and TCM health characteristics; we then use an interactive optimization method, in which TCM experts develop different TCM prevention programs for different clusters, and a heuristic algorithm is used to optimize the programs under the resource constraints. We demonstrate the computational efficiency of the proposed method and report its successful application to TCM-based prevention of COVID-19 in 12 communities in Zhejiang province, China, during the peak of the pandemic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2007.13926v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "based on syndrome differentiation\" principle of TCM", "comparison": "of the novel coronavirus pneumonia"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": true}}}, {"id": "2002.08575v1", "source": "ArXiv", "title": "Syndrome-aware Herb Recommendation with Multi-Graph Convolution Network", "abstract": "Herb recommendation plays a crucial role in the therapeutic process of Traditional Chinese Medicine(TCM), which aims to recommend a set of herbs to treat the symptoms of a patient. While several machine learning methods have been developed for herb recommendation, they are limited in modeling only the interactions between herbs and symptoms, and ignoring the intermediate process of syndrome induction. When performing TCM diagnostics, an experienced doctor typically induces syndromes from the patient's symptoms and then suggests herbs based on the induced syndromes. As such, we believe the induction of syndromes, an overall description of the symptoms, is important for herb recommendation and should be properly handled. However, due to the ambiguity and complexity of syndrome induction, most prescriptions lack the explicit ground truth of syndromes. In this paper, we propose a new method that takes the implicit syndrome induction process into account for herb recommendation. Given a set of symptoms to treat, we aim to generate an overall syndrome representation by effectively fusing the embeddings of all the symptoms in the set, to mimic how a doctor induces the syndromes. Towards symptom embedding learning, we additionally construct a symptom-symptom graph from the input prescriptions for capturing the relations between symptoms; we then build graph convolution networks(GCNs) on both symptom-symptom and symptom-herb graphs to learn symptom embedding. Similarly, we construct a herb-herb graph and build GCNs on both herb-herb and symptom-herb graphs to learn herb embedding, which is finally interacted with the syndrome representation to predict the scores of herbs. In this way, more comprehensive representations can be obtained. We conduct extensive experiments on a public TCM dataset, showing significant improvements over state-of-the-art herb recommendation methods.", "authors": ["Yuanyuan Jin", "<PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2002.08575v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2004.14114v1", "source": "ArXiv", "title": "Decoding SARS-CoV-2 transmission, evolution and ramification on COVID-19 diagnosis, vaccine, and medicine", "abstract": "Tremendous effort has been given to the development of diagnostic tests, preventive vaccines, and therapeutic medicines for coronavirus disease 2019 (COVID-19) caused by severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2). Much of this development has been based on the reference genome collected on January 5, 2020. Based on the genotyping of 6156 genome samples collected up to April 24, 2020, we report that SARS-CoV-2 has had 4459 alarmingly mutations which can be clustered into five subtypes. We introduce mutation ratio and mutation $h$-index to characterize the protein conservativeness and unveil that SARS-CoV-2 envelope protein, main protease, and endoribonuclease protein are relatively conservative, while SARS-CoV-2 nucleocapsid protein, spike protein, and papain-like protease are relatively non-conservative. In particular, the nucleocapsid protein has more than half its genes changed in the past few months, signaling devastating impacts on the ongoing development of COVID-19 diagnosis, vaccines, and drugs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2004.14114v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "ngly mutations which can be clustered into five subtypes"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2007.03616v1", "source": "ArXiv", "title": "Artificial Stupidity", "abstract": "Public debate about AI is dominated by Frankenstein Syndrome, the fear that AI will become superhuman and escape human control. Although superintelligence is certainly a possibility, the interest it excites can distract the public from a more imminent concern: the rise of Artificial Stupidity (AS). This article discusses the roots of Frankenstein Syndrome in <PERSON>'s famous novel of 1818. It then provides a philosophical framework for analysing the stupidity of artificial agents, demonstrating that modern intelligent systems can be seen to suffer from 'stupidity of judgement'. Finally it identifies an alternative literary tradition that exposes the perils and benefits of AS. In the writings of <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ASs replace, oppress or seduce their human users. More optimistically, <PERSON> and <PERSON> imagine ASs that can serve human intellect as maps or as pipes. These writers provide a strong counternarrative to the myths that currently drive the AI debate. They identify ways in which even stupid artificial agents can evade human control, for instance by appealing to stereotypes or distancing us from reality. And they underscore the continuing importance of the literary imagination in an increasingly automated society.", "authors": ["<PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2007.03616v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": true}}}, {"id": "2010.03886v1", "source": "ArXiv", "title": "Rapid and sensitive detection of SARS-CoV-2 with functionalized magnetic nanoparticles", "abstract": "The outbreak of the severe acute respiratory syndrome coronavirus 2 (SARS-CoV-2) threatens global medical systems and economies, and rules our daily living life. Controlling the outbreak of SARS-CoV-2 has become one of the most important and urgent strategies throughout the whole world. As of October, 2020, there have not yet been any medicines or therapies to be effective against SARS-CoV-2. Thus, rapid and sensitive diagnostics is the most important measures to control the outbreak of SARS-CoV-2. Homogeneous biosensing based on magnetic nanoparticles (MNPs) is one of the most promising approaches for rapid and highly sensitive detection of biomolecules. This paper proposes an approach for rapid and sensitive detection of SARS-CoV-2 with functionalized MNPs via the measurement of their magnetic response in an ac magnetic field. Experimental results demonstrate that the proposed approach allows the rapid detection of mimic SARS-CoV-2 with a limit of detection of 0.084 nM (5.9 fmole). The proposed approach has great potential for designing a low-cost and point-of-care device for rapid and sensitive diagnostics of SARS-CoV-2.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2010.03886v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"comparison": "ling the outbreak of SARS-CoV-2 has become one of the most important; the outbreak of SARS-CoV-2"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": true}}}]