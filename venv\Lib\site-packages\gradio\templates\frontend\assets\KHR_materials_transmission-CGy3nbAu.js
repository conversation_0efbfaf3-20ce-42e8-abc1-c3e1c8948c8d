import{ar as o,an as d,ao as p,O as l,b as c,as as _}from"./index-SLPGw9aX.js";import{GLTFLoader as f}from"./glTFLoader-CeZAwvqd.js";import{C as T}from"./objectModelMapping-Duj8W7QQ.js";import"./index-Co_Q4qaw.js";import"./svelte/svelte.js";import"./bone-Ceu782jt.js";import"./rawTexture-BO6WiE_K.js";import"./assetContainer-9eHOOeSf.js";class h{static _GetDefaultOptions(){return{renderSize:1024,samples:4,lodGenerationScale:1,lodGenerationOffset:-4,renderTargetTextureType:T.TEXTURETYPE_HALF_FLOAT,generateMipmaps:!0}}constructor(e,s){this._opaqueRenderTarget=null,this._opaqueMeshesCache=[],this._transparentMeshesCache=[],this._materialObservers={},this._options={...h._GetDefaultOptions(),...e},this._scene=s,this._scene._transmissionHelper=this,this.onErrorObservable=new l,this._scene.onDisposeObservable.addOnce(()=>{this.dispose()}),this._parseScene(),this._setupRenderTargets()}updateOptions(e){if(!Object.keys(e).filter(t=>this._options[t]!==e[t]).length)return;const r={...this._options,...e},a=this._options;this._options=r,r.renderSize!==a.renderSize||r.renderTargetTextureType!==a.renderTargetTextureType||r.generateMipmaps!==a.generateMipmaps||!this._opaqueRenderTarget?this._setupRenderTargets():(this._opaqueRenderTarget.samples=r.samples,this._opaqueRenderTarget.lodGenerationScale=r.lodGenerationScale,this._opaqueRenderTarget.lodGenerationOffset=r.lodGenerationOffset)}getOpaqueTarget(){return this._opaqueRenderTarget}_shouldRenderAsTransmission(e){return e?!!(e instanceof o&&e.subSurface.isRefractionEnabled):!1}_addMesh(e){this._materialObservers[e.uniqueId]=e.onMaterialChangedObservable.add(this._onMeshMaterialChanged.bind(this)),c.SetImmediate(()=>{this._shouldRenderAsTransmission(e.material)?(e.material.refractionTexture=this._opaqueRenderTarget,this._transparentMeshesCache.indexOf(e)===-1&&this._transparentMeshesCache.push(e)):this._opaqueMeshesCache.indexOf(e)===-1&&this._opaqueMeshesCache.push(e)})}_removeMesh(e){e.onMaterialChangedObservable.remove(this._materialObservers[e.uniqueId]),delete this._materialObservers[e.uniqueId];let s=this._transparentMeshesCache.indexOf(e);s!==-1&&this._transparentMeshesCache.splice(s,1),s=this._opaqueMeshesCache.indexOf(e),s!==-1&&this._opaqueMeshesCache.splice(s,1)}_parseScene(){this._scene.meshes.forEach(this._addMesh.bind(this)),this._scene.onNewMeshAddedObservable.add(this._addMesh.bind(this)),this._scene.onMeshRemovedObservable.add(this._removeMesh.bind(this))}_onMeshMaterialChanged(e){const s=this._transparentMeshesCache.indexOf(e),r=this._opaqueMeshesCache.indexOf(e);this._shouldRenderAsTransmission(e.material)?(e.material instanceof o&&(e.material.subSurface.refractionTexture=this._opaqueRenderTarget),r!==-1?(this._opaqueMeshesCache.splice(r,1),this._transparentMeshesCache.push(e)):s===-1&&this._transparentMeshesCache.push(e)):s!==-1?(this._transparentMeshesCache.splice(s,1),this._opaqueMeshesCache.push(e)):r===-1&&this._opaqueMeshesCache.push(e)}_isRenderTargetValid(){return this._opaqueRenderTarget?.getInternalTexture()!==null}_setupRenderTargets(){this._opaqueRenderTarget&&this._opaqueRenderTarget.dispose(),this._opaqueRenderTarget=new _("opaqueSceneTexture",this._options.renderSize,this._scene,this._options.generateMipmaps,void 0,this._options.renderTargetTextureType),this._opaqueRenderTarget.ignoreCameraViewport=!0,this._opaqueRenderTarget.renderList=this._opaqueMeshesCache,this._opaqueRenderTarget.clearColor=this._options.clearColor?.clone()??this._scene.clearColor.clone(),this._opaqueRenderTarget.gammaSpace=!1,this._opaqueRenderTarget.lodGenerationScale=this._options.lodGenerationScale,this._opaqueRenderTarget.lodGenerationOffset=this._options.lodGenerationOffset,this._opaqueRenderTarget.samples=this._options.samples,this._opaqueRenderTarget.renderSprites=!0,this._opaqueRenderTarget.renderParticles=!0,this._opaqueRenderTarget.disableImageProcessing=!0;let e;this._opaqueRenderTarget.onBeforeBindObservable.add(s=>{e=this._scene.environmentIntensity,this._scene.environmentIntensity=1,this._options.clearColor?s.clearColor.copyFrom(this._options.clearColor):this._scene.clearColor.toLinearSpaceToRef(s.clearColor,this._scene.getEngine().useExactSrgbConversions)}),this._opaqueRenderTarget.onAfterUnbindObservable.add(()=>{this._scene.environmentIntensity=e}),this._transparentMeshesCache.forEach(s=>{this._shouldRenderAsTransmission(s.material)&&(s.material.refractionTexture=this._opaqueRenderTarget)})}dispose(){this._scene._transmissionHelper=void 0,this._opaqueRenderTarget&&(this._opaqueRenderTarget.dispose(),this._opaqueRenderTarget=null),this._transparentMeshesCache=[],this._opaqueMeshesCache=[]}}const i="KHR_materials_transmission";class g{constructor(e){this.name=i,this.order=175,this._loader=e,this.enabled=this._loader.isExtensionUsed(i),this.enabled&&(e.parent.transparencyAsCoverage=!0)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,s,r){return f.LoadExtensionAsync(e,s,this.name,(a,t)=>{const n=new Array;return n.push(this._loader.loadMaterialPropertiesAsync(e,s,r)),n.push(this._loadTransparentPropertiesAsync(a,s,r,t)),Promise.all(n).then(()=>{})})}_loadTransparentPropertiesAsync(e,s,r,a){if(!(r instanceof o))throw new Error(`${e}: Material type not supported`);const t=r;if(t.subSurface.isRefractionEnabled=!0,t.subSurface.volumeIndexOfRefraction=1,t.subSurface.useAlbedoToTintRefraction=!0,a.transmissionFactor!==void 0){t.subSurface.refractionIntensity=a.transmissionFactor;const n=t.getScene();t.subSurface.refractionIntensity&&!n._transmissionHelper?new h({},t.getScene()):t.subSurface.refractionIntensity&&!n._transmissionHelper?._isRenderTargetValid()&&n._transmissionHelper?._setupRenderTargets()}else return t.subSurface.refractionIntensity=0,t.subSurface.isRefractionEnabled=!1,Promise.resolve();return t.subSurface.minimumThickness=0,t.subSurface.maximumThickness=0,a.transmissionTexture?(a.transmissionTexture.nonColorData=!0,this._loader.loadTextureInfoAsync(`${e}/transmissionTexture`,a.transmissionTexture,void 0).then(n=>{n.name=`${r.name} (Transmission)`,t.subSurface.refractionIntensityTexture=n,t.subSurface.useGltfStyleTextures=!0})):Promise.resolve()}}d(i);p(i,!0,u=>new g(u));export{g as KHR_materials_transmission};
//# sourceMappingURL=KHR_materials_transmission-CGy3nbAu.js.map
