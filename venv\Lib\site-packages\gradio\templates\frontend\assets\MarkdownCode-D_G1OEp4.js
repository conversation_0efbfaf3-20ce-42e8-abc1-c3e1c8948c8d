const __vite__fileDeps=["./mermaid.core-2mGLstM8.js","./index-Co_Q4qaw.js","./index-YT7-ZTht.css","./dispatch-kxCwF96_.js","./step-Ce-xBr2D.js","./select-BigU4G0v.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as S}from"./index-Co_Q4qaw.js";import{k as y,A as I,c as q}from"./MarkdownCode.svelte_svelte_type_style_lang-DwuBo7pZ.js";var B=function(e,r,t){for(var a=t,l=0,s=e.length;a<r.length;){var o=r[a];if(l<=0&&r.slice(a,a+s)===e)return a;o==="\\"?a++:o==="{"?l++:o==="}"&&l--,a++}return-1},H=function(e){return e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},N=/^\\begin{/,G=function(e,r){for(var t,a=[],l=new RegExp("("+r.map(h=>H(h.left)).join("|")+")");t=e.search(l),t!==-1;){t>0&&(a.push({type:"text",data:e.slice(0,t)}),e=e.slice(t));var s=r.findIndex(h=>e.startsWith(h.left));if(t=B(r[s].right,e,r[s].left.length),t===-1)break;var o=e.slice(0,t+r[s].right.length),f=N.test(o)?o:e.slice(r[s].left.length,t);a.push({type:"math",data:f,rawData:o,display:r[s].display}),e=e.slice(t+r[s].right.length)}return e!==""&&a.push({type:"text",data:e}),a},X=function(e,r){var t=G(e,r.delimiters);if(t.length===1&&t[0].type==="text")return null;for(var a=document.createDocumentFragment(),l=0;l<t.length;l++)if(t[l].type==="text")a.appendChild(document.createTextNode(t[l].data));else{var s=document.createElement("span"),o=t[l].data;r.displayMode=t[l].display;try{r.preProcess&&(o=r.preProcess(o)),y.render(o,s,r)}catch(f){if(!(f instanceof y.ParseError))throw f;r.errorCallback("KaTeX auto-render: Failed to parse `"+t[l].data+"` with ",f),a.appendChild(document.createTextNode(t[l].rawData));continue}a.appendChild(s)}return a},K=function n(e,r){for(var t=0;t<e.childNodes.length;t++){var a=e.childNodes[t];if(a.nodeType===3){for(var l=a.textContent,s=a.nextSibling,o=0;s&&s.nodeType===Node.TEXT_NODE;)l+=s.textContent,s=s.nextSibling,o++;var f=X(l,r);if(f){for(var h=0;h<o;h++)a.nextSibling.remove();t+=f.childNodes.length-1,e.replaceChild(f,a)}else t+=o}else a.nodeType===1&&function(){var p=" "+a.className+" ",w=r.ignoredTags.indexOf(a.nodeName.toLowerCase())===-1&&r.ignoredClasses.every(m=>p.indexOf(" "+m+" ")===-1);w&&n(a,r)}()}},U=function(e,r){if(!e)throw new Error("No element provided to render");var t={};for(var a in r)r.hasOwnProperty(a)&&(t[a]=r[a]);t.delimiters=t.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],t.ignoredTags=t.ignoredTags||["script","noscript","style","textarea","pre","code","option"],t.ignoredClasses=t.ignoredClasses||[],t.errorCallback=t.errorCallback||console.error,t.macros=t.macros||{},K(e,t)};const j=(n,e)=>{try{return!!n&&new URL(n).origin!==new URL(e).origin}catch{return!1}};function x(n,e){const r=new I,t=new DOMParser().parseFromString(n,"text/html");return M(t.body,"A",a=>{a instanceof HTMLElement&&"target"in a&&j(a.getAttribute("href"),e)&&(a.setAttribute("target","_blank"),a.setAttribute("rel","noopener noreferrer"))}),r.sanitize(t).body.innerHTML}function M(n,e,r){n&&(n.nodeName===e||typeof e=="function")&&r(n);const t=n?.childNodes||[];for(let a=0;a<t.length;a++)M(t[a],e,r)}const T=["!--","!doctype","a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","big","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","menu","meta","meter","nav","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","search","section","select","small","source","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr"],V=["g","defs","use","symbol","rect","circle","ellipse","line","polyline","polygon","path","image","text","tspan","textPath","linearGradient","radialGradient","stop","pattern","clipPath","mask","filter","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feGaussianBlur","feMerge","feMorphology","feOffset","feSpecularLighting","feTurbulence","feMergeNode","feFuncR","feFuncG","feFuncB","feFuncA","feDistantLight","fePointLight","feSpotLight","feFlood","feTile","animate","animateTransform","animateMotion","mpath","set","view","cursor","foreignObject","desc","title","metadata","switch"],Z=[...T,...V.filter(n=>!T.includes(n))],{SvelteComponent:W,attr:J,binding_callbacks:Q,detach:Y,element:$,flush:_,init:ee,insert:te,noop:E,safe_not_equal:re,toggle_class:v}=window.__gradio__svelte__internal,{afterUpdate:ae,tick:ne,onMount:ce}=window.__gradio__svelte__internal;function ie(n){let e;return{c(){e=$("span"),J(e,"class","md svelte-7ddecg"),v(e,"chatbot",n[0]),v(e,"prose",n[1])},m(r,t){te(r,e,t),e.innerHTML=n[3],n[11](e)},p(r,[t]){t&8&&(e.innerHTML=r[3]),t&1&&v(e,"chatbot",r[0]),t&2&&v(e,"prose",r[1])},i:E,o:E,d(r){r&&Y(e),n[11](null)}}}function L(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function se(n,e,r){let{chatbot:t=!0}=e,{message:a}=e,{sanitize_html:l=!0}=e,{latex_delimiters:s=[]}=e,{render_markdown:o=!0}=e,{line_breaks:f=!0}=e,{header_links:h=!1}=e,{allow_tags:p=!1}=e,{theme_mode:w="system"}=e,m,k;const A=q({header_links:h,line_breaks:f,latex_delimiters:s||[]});function C(i,d){if(d===!0){const g=/<\/?([a-zA-Z][a-zA-Z0-9-]*)([\s>])/g;return i.replace(g,(c,u,b)=>Z.includes(u.toLowerCase())?c:c.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}if(Array.isArray(d)){const g=d.map(u=>({open:new RegExp(`<(${u})(\\s+[^>]*)?>`,"gi"),close:new RegExp(`</(${u})>`,"gi")}));let c=i;return g.forEach(u=>{c=c.replace(u.open,b=>b.replace(/</g,"&lt;").replace(/>/g,"&gt;")),c=c.replace(u.close,b=>b.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}),c}return i}function D(i){let d=i;if(o){const g=[];s.forEach((c,u)=>{const b=L(c.left),z=L(c.right),P=new RegExp(`${b}([\\s\\S]+?)${z}`,"g");d=d.replace(P,(F,le)=>(g.push(F),`%%%LATEX_BLOCK_${g.length-1}%%%`))}),d=A.parse(d),d=d.replace(/%%%LATEX_BLOCK_(\d+)%%%/g,(c,u)=>g[parseInt(u,10)])}return p&&(d=C(d,p)),l&&x&&(d=x(d)),d}async function O(i){if(s.length>0&&i&&s.some(g=>i.includes(g.left)&&i.includes(g.right))&&U(m,{delimiters:s,throwOnError:!1}),m){const d=m.querySelectorAll(".mermaid");if(d.length>0){await ne();const{default:g}=await S(()=>import("./mermaid.core-2mGLstM8.js").then(c=>c.b3),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url);g.initialize({startOnLoad:!1,theme:w==="dark"?"dark":"default",securityLevel:"antiscript"}),await g.run({nodes:Array.from(d).map(c=>c)})}}}ae(async()=>{m&&document.body.contains(m)?await O(a):console.error("Element is not in the DOM")});function R(i){Q[i?"unshift":"push"](()=>{m=i,r(2,m)})}return n.$$set=i=>{"chatbot"in i&&r(0,t=i.chatbot),"message"in i&&r(4,a=i.message),"sanitize_html"in i&&r(5,l=i.sanitize_html),"latex_delimiters"in i&&r(6,s=i.latex_delimiters),"render_markdown"in i&&r(1,o=i.render_markdown),"line_breaks"in i&&r(7,f=i.line_breaks),"header_links"in i&&r(8,h=i.header_links),"allow_tags"in i&&r(9,p=i.allow_tags),"theme_mode"in i&&r(10,w=i.theme_mode)},n.$$.update=()=>{n.$$.dirty&16&&(a&&a.trim()?r(3,k=D(a)):r(3,k=""))},[t,o,m,k,a,l,s,f,h,p,w,R]}class fe extends W{constructor(e){super(),ee(this,e,se,ie,re,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8,allow_tags:9,theme_mode:10})}get chatbot(){return this.$$.ctx[0]}set chatbot(e){this.$$set({chatbot:e}),_()}get message(){return this.$$.ctx[4]}set message(e){this.$$set({message:e}),_()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),_()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),_()}get render_markdown(){return this.$$.ctx[1]}set render_markdown(e){this.$$set({render_markdown:e}),_()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),_()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),_()}get allow_tags(){return this.$$.ctx[9]}set allow_tags(e){this.$$set({allow_tags:e}),_()}get theme_mode(){return this.$$.ctx[10]}set theme_mode(e){this.$$set({theme_mode:e}),_()}}export{fe as M};
//# sourceMappingURL=MarkdownCode-D_G1OEp4.js.map
