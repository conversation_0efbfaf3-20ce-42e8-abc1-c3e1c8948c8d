import{an as n,ao as r}from"./index-SLPGw9aX.js";import"./index-Co_Q4qaw.js";import"./svelte/svelte.js";const o="KHR_xmp_json_ld";class i{constructor(t){this.name=o,this.order=100,this._loader=t,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}onLoading(){if(this._loader.rootBabylonMesh===null)return;const t=this._loader.gltf.extensions?.KHR_xmp_json_ld,e=this._loader.gltf.asset?.extensions?.KHR_xmp_json_ld;if(t&&e){const a=+e.packet;t.packets&&a<t.packets.length&&(this._loader.rootBabylonMesh.metadata=this._loader.rootBabylonMesh.metadata||{},this._loader.rootBabylonMesh.metadata.xmp=t.packets[a])}}}n(o);r(o,!0,s=>new i(s));export{i as KHR_xmp_json_ld};
//# sourceMappingURL=KHR_xmp_json_ld-DyvKp14p.js.map
